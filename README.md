# LegalAI - Contract Management System

A comprehensive, AI-powered contract management platform built with modern web technologies. LegalAI streamlines the entire contract lifecycle from creation to execution, providing organizations with a secure, scalable, and user-friendly interface for managing legal contracts efficiently.

## 🚀 Features

### Core Functionality
- **Multi-step Contract Wizard** - Guided contract creation with real-time preview
- **AI-Powered Analysis** - Contract risk assessment and compliance checking
- **Document Repository** - Centralized storage with folder management and search
- **Template Library** - Reusable contract templates with categorization
- **Approval Workflows** - Configurable approval processes with role-based permissions
- **Electronic Signatures** - Integrated signing workflow with status tracking
- **Multi-workspace Support** - Team-based collaboration with data isolation
- **Analytics Dashboard** - Comprehensive reporting and data visualization

### Advanced Features
- **Smart Clause Suggestions** - AI-powered clause recommendations
- **Document Import/Export** - Support for PDF, Word, and text documents
- **Real-time Collaboration** - Live document editing and commenting
- **Audit Logging** - Comprehensive activity tracking
- **Mobile Responsive** - Optimized for all device sizes
- **Dark Mode Support** - Consistent theming across the application

## 🛠 Technology Stack

### Frontend
- **React 18+** with TypeScript for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **Shadcn/ui** for high-quality, accessible components
- **TipTap** for rich text editing
- **React Router** for client-side routing
- **React Hook Form** with Zod validation

### Backend
- **FastAPI** with Python for high-performance APIs
- **PostgreSQL** via Supabase with Row Level Security
- **Clerk** for authentication and user management
- **Supabase Storage** for secure document management
- **Pydantic** for data validation and serialization

### Infrastructure
- **Supabase** for database and storage
- **Clerk** for authentication and multi-workspace support
- **Serverless architecture** for scalability
- **CORS configuration** for secure cross-origin requests

## 🚦 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.8+
- Supabase account
- Clerk account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LegalAIV4
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Install backend dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. **Environment Setup**

   Create `.env` in the root directory:
   ```bash
   VITE_CLERK_PUBLISHABLE_KEY=pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ
   ```

   Create `backend/.env`:
   ```bash
   SUPABASE_URL=https://kdcjdbufciuvvznqnotx.supabase.co
   SUPABASE_KEY=your_supabase_key
   CLERK_SECRET_KEY=your_clerk_secret_key
   CLERK_PUBLISHABLE_KEY=pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ
   API_PREFIX=/api
   ENVIRONMENT=production
   ```

5. **Database Setup**
   ```bash
   cd backend
   python apply_schema.py
   python seed_demo_data.py
   ```

6. **Start Development Servers**

   Frontend (Terminal 1):
   ```bash
   npm run dev
   # Runs on http://localhost:5173
   ```

   Backend (Terminal 2):
   ```bash
   cd backend
   python run.py
   # Runs on http://localhost:8000
   ```

## 🧪 Demo Credentials

**Test Account:**
- Email: `<EMAIL>`
- Password: `.r3l+_Ajj`

**Demo Data Includes:**
- 5 demo users with various roles
- 4 demo workspaces (Tech, Legal, HR, Consulting)
- 3 contract templates (NDA, Service Agreement, Employment)
- Sample contracts with realistic data

## 📁 Project Structure

```
LegalAIV4/
├── src/
│   ├── components/          # React components
│   │   ├── contracts/       # Contract management
│   │   ├── workspace/       # Workspace management
│   │   ├── repository/      # Document repository
│   │   ├── approvals/       # Approval workflows
│   │   ├── analytics/       # Data visualization
│   │   ├── layout/          # Layout components
│   │   └── ui/              # Base UI components
│   ├── lib/                 # Utility libraries
│   ├── services/            # API service layer
│   ├── types/               # TypeScript definitions
│   └── routes.tsx           # Application routing
├── backend/
│   ├── app/
│   │   ├── api/             # API endpoints
│   │   ├── core/            # Authentication & config
│   │   ├── db/              # Database schema
│   │   ├── schemas/         # Pydantic models
│   │   └── services/        # Business logic
│   └── requirements.txt     # Python dependencies
├── docs/                    # Documentation
└── README.md               # This file
```

## 🔧 Development

### Available Scripts

**Frontend:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

**Backend:**
- `python run.py` - Start development server
- `python apply_schema.py` - Apply database schema
- `python seed_demo_data.py` - Seed demo data

### Key Components

**Contract Wizard** (`/contracts/wizard`)
- Multi-step contract creation process
- Real-time document preview with TipTap editor
- AI-powered suggestions and analysis
- Template integration and customization

**Document Repository** (`/repository`)
- Folder-based document organization
- Grid and list view options
- Advanced search and filtering
- Document preview and editing

**Workspace Management** (`/workspaces`)
- Multi-tenant architecture with Clerk Organizations
- Role-based access control
- Optimized workspace switching with caching

## 🔐 Security

- **Authentication**: Clerk JWT validation
- **Authorization**: Workspace-based access control
- **Data Isolation**: Row Level Security (RLS) policies
- **File Security**: Signed URLs with expiration
- **Input Validation**: Comprehensive validation with Pydantic and Zod

## 📊 API Documentation

The backend provides comprehensive REST API endpoints:

- **Workspaces**: `/api/workspaces` - Workspace CRUD operations
- **Contracts**: `/api/contracts` - Contract management
- **Templates**: `/api/templates` - Template operations
- **Documents**: `/api/documents` - Document handling
- **Storage**: `/api/storage` - File storage operations
- **Analytics**: `/api/analytics` - Dashboard metrics

All endpoints include workspace filtering and authentication validation.

## 🚀 Deployment

The application is designed for serverless deployment:

1. **Frontend**: Deploy to Vercel, Netlify, or similar
2. **Backend**: Deploy to Railway, Render, or containerized platforms
3. **Database**: Supabase PostgreSQL (already configured)
4. **Storage**: Supabase Storage (already configured)

## 📚 Documentation

- **Technical Documentation**: `docs/legalai.md`
- **Design System**: `docs/design-system.md`
- **Development Guidelines**: `docs/project-rules.md`

## 🤝 Contributing

1. Follow the development guidelines in `docs/project-rules.md`
2. Always update documentation when adding features
3. Test thoroughly before submitting changes
4. Maintain the established UI design principles

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support or questions:
- Review the documentation in the `docs/` directory
- Check the component-specific documentation
- Contact the development team

---

**Built with ❤️ using React, TypeScript, FastAPI, and Supabase**
