import React from 'react';
import { X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DocumentPreviewEngine, DocumentPreviewEngineProps } from './DocumentPreviewEngine';
import { DocumentTheme } from '../core/DocumentTypes';

export interface DocumentPreviewModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback when modal should close */
  onClose: () => void;
  /** Document content to display */
  content: string;
  /** Document title for the modal header */
  title?: string;
  /** Theme configuration */
  theme?: Partial<DocumentTheme>;
  /** Additional props to pass to DocumentPreviewEngine */
  previewProps?: Partial<DocumentPreviewEngineProps>;
  /** Custom modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

/**
 * DocumentPreviewModal - Modal wrapper for document previews
 * 
 * Use cases:
 * - Template selection previews
 * - Document approval workflows
 * - Quick document reviews
 * - Contract previews before editing
 */
export const DocumentPreviewModal: React.FC<DocumentPreviewModalProps> = ({
  isOpen,
  onClose,
  content,
  title = 'Document Preview',
  theme,
  previewProps = {},
  size = 'xl'
}) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-[95vw] max-h-[95vh]'
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`${sizeClasses[size]} h-[80vh] p-0 gap-0`}>
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          <DocumentPreviewEngine
            content={content}
            theme={theme}
            documentTitle={title}
            className="h-full"
            {...previewProps}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentPreviewModal;
