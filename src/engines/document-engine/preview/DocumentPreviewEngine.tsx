import React, { useMemo, useRef, useState } from 'react';
import { ZoomIn, ZoomOut, RotateCcw, Printer, Download, Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DocumentTheme, DocumentEngineError } from '../core/DocumentTypes';
import { DocumentEngineErrorBoundary } from '../components/DocumentEngineErrorBoundary';
import { DocumentStyleGenerator } from '../styles/DocumentStyleGenerator';

export interface DocumentPreviewEngineProps {
  /** Document content to display (HTML string) */
  content: string;
  /** Theme configuration for document styling */
  theme?: Partial<DocumentTheme>;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show zoom controls */
  showZoomControls?: boolean;
  /** Whether to show print button */
  showPrintButton?: boolean;
  /** Whether to show download button */
  showDownloadButton?: boolean;
  /** Whether to show fullscreen button */
  showFullscreenButton?: boolean;
  /** Initial zoom level (1.0 = 100%) */
  initialZoom?: number;
  /** Document title for export/print */
  documentTitle?: string;
  /** Callback when print is requested */
  onPrint?: () => void;
  /** Callback when download is requested */
  onDownload?: () => void;
  /** Callback when fullscreen is requested */
  onFullscreen?: () => void;
  /** Error handler */
  onError?: (error: DocumentEngineError) => void;
}

/**
 * DocumentPreviewEngine - Read-only document viewer optimized for preview scenarios
 * 
 * Features:
 * - Non-editable document display with professional styling
 * - Optional zoom controls for better viewing
 * - Print and download functionality
 * - Fullscreen viewing mode
 * - Optimized for performance (no editing overhead)
 * - Consistent styling with editable DocumentEngine
 */
export const DocumentPreviewEngine: React.FC<DocumentPreviewEngineProps> = ({
  content,
  theme,
  className = '',
  showZoomControls = true,
  showPrintButton = true,
  showDownloadButton = true,
  showFullscreenButton = true,
  initialZoom = 1.0,
  documentTitle = 'Document Preview',
  onPrint,
  onDownload,
  onFullscreen,
  onError
}) => {
  const previewRef = useRef<HTMLDivElement>(null);
  const [zoomLevel, setZoomLevel] = useState(initialZoom);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Default theme configuration matching DocumentEngine
  const defaultTheme: DocumentTheme = {
    fontFamily: 'Times New Roman',
    fontSize: '12pt',
    lineHeight: 1.6,
    pageWidth: '8.5in',
    margins: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in'
    },
    colors: {
      text: '#1a1a1a',
      background: '#ffffff',
      border: '#e5e7eb',
      accent: '#3b82f6'
    }
  };

  const appliedTheme = { ...defaultTheme, ...theme };

  // Create style generator for unified styling
  const styleGenerator = useMemo(() => new DocumentStyleGenerator(appliedTheme), [appliedTheme]);

  // Generate document styles using unified system
  const documentStyles = useMemo(() => styleGenerator.generatePreviewStyles(zoomLevel), [styleGenerator, zoomLevel]);

  // Generate base styles for injection into preview content
  const baseStyles = useMemo(() => styleGenerator.generateBaseStyles(), [styleGenerator]);

  // Zoom controls
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.1, 2.0));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.1, 0.5));
  };

  const handleResetZoom = () => {
    setZoomLevel(1.0);
  };

  // Print functionality
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
      return;
    }

    // Default print implementation using unified styling
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printHTML = styleGenerator.generatePrintHTML(content, documentTitle);
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // Download functionality
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
      return;
    }

    // Default download implementation using unified styling
    const htmlContent = styleGenerator.generateExportHTML(content, documentTitle);

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${documentTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Fullscreen functionality
  const handleFullscreen = () => {
    if (onFullscreen) {
      onFullscreen();
      return;
    }

    if (!isFullscreen && previewRef.current) {
      if (previewRef.current.requestFullscreen) {
        previewRef.current.requestFullscreen();
        setIsFullscreen(true);
      }
    } else if (document.exitFullscreen) {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle fullscreen change events
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <DocumentEngineErrorBoundary onError={onError}>
      <div 
        ref={previewRef}
        className={`document-preview-engine flex flex-col h-full bg-gray-50 ${className}`}
      >
        {/* Preview Controls */}
        {(showZoomControls || showPrintButton || showDownloadButton || showFullscreenButton) && (
          <div className="preview-controls flex items-center justify-between p-3 bg-white border-b border-gray-200 shadow-sm">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">{documentTitle}</span>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Zoom Controls */}
              {showZoomControls && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomOut}
                    disabled={zoomLevel <= 0.5}
                    className="h-8 w-8 p-0"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-600 min-w-[3rem] text-center">
                    {Math.round(zoomLevel * 100)}%
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomIn}
                    disabled={zoomLevel >= 2.0}
                    className="h-8 w-8 p-0"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetZoom}
                    className="h-8 w-8 p-0"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Action Buttons */}
              {showPrintButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrint}
                  className="h-8 w-8 p-0"
                >
                  <Printer className="h-4 w-4" />
                </Button>
              )}

              {showDownloadButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}

              {showFullscreenButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFullscreen}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Document Preview Area */}
        <div className="flex-1 overflow-auto bg-gray-50 flex justify-center p-4">
          <div
            className="document-preview-content"
            style={{
              ...documentStyles,
              // Add extra margin for zoom scaling
              marginTop: zoomLevel > 1 ? `${(zoomLevel - 1) * 50}px` : '0',
              marginBottom: zoomLevel > 1 ? `${(zoomLevel - 1) * 50}px` : '0'
            }}
          >
            {/* Inject unified styles for consistent typography */}
            <style dangerouslySetInnerHTML={{ __html: baseStyles }} />
            <div dangerouslySetInnerHTML={{ __html: content }} />
          </div>
        </div>
      </div>
    </DocumentEngineErrorBoundary>
  );
};

export default DocumentPreviewEngine;
