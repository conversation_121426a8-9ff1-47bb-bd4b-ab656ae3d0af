import React from 'react';
import { FileText, Loader2, Download, Save, Zap } from 'lucide-react';

interface DocumentEngineLoaderProps {
  type?: 'initial' | 'saving' | 'exporting' | 'syncing' | 'processing';
  message?: string;
  progress?: number;
  className?: string;
}

/**
 * Loading states for Document Engine operations
 * 
 * Provides different loading indicators for various document engine operations
 * with appropriate messaging and visual feedback.
 */
export const DocumentEngineLoader: React.FC<DocumentEngineLoaderProps> = ({
  type = 'initial',
  message,
  progress,
  className = ''
}) => {
  const getLoadingConfig = () => {
    switch (type) {
      case 'initial':
        return {
          icon: FileText,
          defaultMessage: 'Loading document editor...',
          color: 'text-blue-500',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800'
        };
      case 'saving':
        return {
          icon: Save,
          defaultMessage: 'Saving document...',
          color: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800'
        };
      case 'exporting':
        return {
          icon: Download,
          defaultMessage: 'Exporting document...',
          color: 'text-purple-500',
          bgColor: 'bg-purple-50 dark:bg-purple-900/20',
          borderColor: 'border-purple-200 dark:border-purple-800'
        };
      case 'syncing':
        return {
          icon: Zap,
          defaultMessage: 'Syncing changes...',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800'
        };
      case 'processing':
        return {
          icon: Loader2,
          defaultMessage: 'Processing...',
          color: 'text-slate-500',
          bgColor: 'bg-slate-50 dark:bg-slate-900/20',
          borderColor: 'border-slate-200 dark:border-slate-800'
        };
      default:
        return {
          icon: Loader2,
          defaultMessage: 'Loading...',
          color: 'text-slate-500',
          bgColor: 'bg-slate-50 dark:bg-slate-900/20',
          borderColor: 'border-slate-200 dark:border-slate-800'
        };
    }
  };

  const config = getLoadingConfig();
  const Icon = config.icon;
  const displayMessage = message || config.defaultMessage;

  return (
    <div className={`document-engine-loader flex flex-col items-center justify-center p-8 ${config.bgColor} ${config.borderColor} border rounded-lg ${className}`}>
      <div className="relative">
        <Icon className={`h-12 w-12 ${config.color} ${type !== 'initial' ? 'animate-spin' : ''}`} />
        {progress !== undefined && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-16 h-16 border-4 border-transparent border-t-current rounded-full animate-spin opacity-30" />
          </div>
        )}
      </div>
      
      <div className="mt-4 text-center">
        <p className={`text-sm font-medium ${config.color.replace('text-', 'text-').replace('-500', '-700')} dark:${config.color.replace('-500', '-300')}`}>
          {displayMessage}
        </p>
        
        {progress !== undefined && (
          <div className="mt-2 w-48">
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400 mb-1">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${config.color.replace('text-', 'bg-')}`}
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Skeleton loader for document content
 */
export const DocumentContentSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`document-content-skeleton animate-pulse p-8 ${className}`}>
    {/* Document header skeleton */}
    <div className="text-center mb-8">
      <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-3/4 mx-auto mb-2" />
      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 mx-auto mb-1" />
      <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-1/3 mx-auto" />
    </div>

    {/* Document content skeleton */}
    <div className="space-y-4">
      {/* Paragraph skeletons */}
      {[...Array(6)].map((_, i) => (
        <div key={i} className="space-y-2">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-full" />
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-5/6" />
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-4/5" />
        </div>
      ))}

      {/* Section heading skeleton */}
      <div className="mt-8 mb-4">
        <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded w-1/2" />
      </div>

      {/* More paragraph skeletons */}
      {[...Array(4)].map((_, i) => (
        <div key={i + 6} className="space-y-2">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-full" />
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4" />
        </div>
      ))}

      {/* Table skeleton */}
      <div className="mt-8 border border-slate-200 dark:border-slate-700 rounded">
        <div className="grid grid-cols-3 gap-4 p-4 bg-slate-100 dark:bg-slate-800">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-4 bg-slate-200 dark:bg-slate-700 rounded" />
          ))}
        </div>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="grid grid-cols-3 gap-4 p-4 border-t border-slate-200 dark:border-slate-700">
            {[...Array(3)].map((_, j) => (
              <div key={j} className="h-4 bg-slate-200 dark:bg-slate-700 rounded" />
            ))}
          </div>
        ))}
      </div>
    </div>
  </div>
);

/**
 * Toolbar skeleton loader
 */
export const DocumentToolbarSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`document-toolbar-skeleton animate-pulse border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-950 p-2 ${className}`}>
    <div className="flex items-center gap-2">
      {/* File operations */}
      <div className="flex items-center gap-1 mr-2">
        <div className="h-8 w-16 bg-slate-200 dark:bg-slate-700 rounded" />
        <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 rounded" />
      </div>

      <div className="w-px h-6 bg-slate-200 dark:bg-slate-700 mx-1" />

      {/* Undo/Redo */}
      <div className="flex items-center gap-1 mr-2">
        <div className="h-8 w-8 bg-slate-200 dark:bg-slate-700 rounded" />
        <div className="h-8 w-8 bg-slate-200 dark:bg-slate-700 rounded" />
      </div>

      <div className="w-px h-6 bg-slate-200 dark:bg-slate-700 mx-1" />

      {/* Font controls */}
      <div className="flex items-center gap-1 mr-2">
        <div className="h-8 w-32 bg-slate-200 dark:bg-slate-700 rounded" />
        <div className="h-8 w-16 bg-slate-200 dark:bg-slate-700 rounded" />
      </div>

      <div className="w-px h-6 bg-slate-200 dark:bg-slate-700 mx-1" />

      {/* Formatting buttons */}
      <div className="flex items-center gap-1">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-8 w-8 bg-slate-200 dark:bg-slate-700 rounded" />
        ))}
      </div>
    </div>
  </div>
);

/**
 * Combined loading state for the entire document engine
 */
export const DocumentEngineLoadingState: React.FC<{
  type?: DocumentEngineLoaderProps['type'];
  message?: string;
  progress?: number;
  showSkeleton?: boolean;
  className?: string;
}> = ({ 
  type = 'initial', 
  message, 
  progress, 
  showSkeleton = false, 
  className = '' 
}) => {
  if (showSkeleton) {
    return (
      <div className={`document-engine-loading h-full flex flex-col ${className}`}>
        <DocumentToolbarSkeleton />
        <div className="flex-1 overflow-hidden bg-gray-50 flex justify-center">
          <div className="w-full max-w-4xl">
            <DocumentContentSkeleton />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`document-engine-loading h-full flex items-center justify-center ${className}`}>
      <DocumentEngineLoader 
        type={type} 
        message={message} 
        progress={progress} 
      />
    </div>
  );
};

export default DocumentEngineLoader;
