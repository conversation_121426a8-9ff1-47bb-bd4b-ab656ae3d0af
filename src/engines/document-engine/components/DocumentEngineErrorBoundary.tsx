import React, { Component, ErrorInfo, ReactNode } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle, Refresh<PERSON>w, FileText, Bug } from 'lucide-react';
import { DocumentEngineError } from '../core/DocumentTypes';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: DocumentEngineError) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * Error Boundary for Document Engine
 * 
 * Catches JavaScript errors anywhere in the document engine component tree,
 * logs those errors, and displays a fallback UI instead of crashing.
 */
export class DocumentEngineErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('Document Engine Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report error to parent component
    if (this.props.onError) {
      const documentEngineError: DocumentEngineError = {
        code: 'COMPONENT_ERROR',
        message: error.message,
        details: {
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          errorId: this.state.errorId
        },
        timestamp: new Date()
      };
      this.props.onError(documentEngineError);
    }

    // Report to error tracking service (if available)
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack
          }
        },
        tags: {
          component: 'DocumentEngine',
          errorId: this.state.errorId
        }
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  handleReportBug = () => {
    const { error, errorInfo, errorId } = this.state;
    const bugReport = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    // Copy to clipboard for easy reporting
    navigator.clipboard.writeText(JSON.stringify(bugReport, null, 2)).then(() => {
      alert('Bug report copied to clipboard. Please paste it when reporting the issue.');
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="document-engine-error flex flex-col items-center justify-center h-full p-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="text-center max-w-md">
            <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            
            <h2 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">
              Document Engine Error
            </h2>
            
            <p className="text-red-600 dark:text-red-300 mb-4">
              Something went wrong with the document editor. This is likely a temporary issue.
            </p>

            <div className="bg-red-100 dark:bg-red-900/40 border border-red-200 dark:border-red-700 rounded p-3 mb-4 text-left">
              <p className="text-sm text-red-700 dark:text-red-300 font-mono">
                Error ID: {this.state.errorId}
              </p>
              {this.state.error && (
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {this.state.error.message}
                </p>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button
                onClick={this.handleRetry}
                variant="default"
                size="sm"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              
              <Button
                onClick={this.handleReportBug}
                variant="outline"
                size="sm"
                className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/20"
              >
                <Bug className="h-4 w-4 mr-2" />
                Report Bug
              </Button>
            </div>

            <div className="mt-6 text-xs text-red-500 dark:text-red-400">
              <p>If this problem persists, try refreshing the page or contact support.</p>
            </div>
          </div>

          {/* Development mode: Show detailed error info */}
          {import.meta.env.DEV && this.state.error && (
            <details className="mt-8 w-full max-w-2xl">
              <summary className="cursor-pointer text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
                Show Error Details (Development)
              </summary>
              <div className="mt-2 p-4 bg-red-100 dark:bg-red-900/40 border border-red-200 dark:border-red-700 rounded text-xs font-mono overflow-auto max-h-64">
                <div className="mb-4">
                  <strong>Error:</strong>
                  <pre className="mt-1 text-red-800 dark:text-red-200">{this.state.error.stack}</pre>
                </div>
                {this.state.errorInfo && (
                  <div>
                    <strong>Component Stack:</strong>
                    <pre className="mt-1 text-red-800 dark:text-red-200">{this.state.errorInfo.componentStack}</pre>
                  </div>
                )}
              </div>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version of error boundary for functional components
 */
export function useDocumentEngineErrorHandler() {
  const [error, setError] = React.useState<DocumentEngineError | null>(null);

  const handleError = React.useCallback((error: DocumentEngineError) => {
    setError(error);
    console.error('Document Engine Error:', error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    handleError,
    clearError
  };
}

/**
 * Simple error fallback component
 */
export const SimpleErrorFallback: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <div className="flex flex-col items-center justify-center h-32 p-4 bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded">
    <FileText className="h-8 w-8 text-slate-400 mb-2" />
    <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">Unable to load document editor</p>
    {onRetry && (
      <Button onClick={onRetry} variant="outline" size="sm">
        <RefreshCw className="h-3 w-3 mr-1" />
        Retry
      </Button>
    )}
  </div>
);

export default DocumentEngineErrorBoundary;
