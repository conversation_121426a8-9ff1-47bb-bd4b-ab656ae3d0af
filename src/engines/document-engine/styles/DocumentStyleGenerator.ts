import { DocumentTheme } from '../core/DocumentTypes';

/**
 * Unified document style generator for consistent styling across preview and export
 * Ensures WYSIWYG experience between preview and exported documents
 */
export class DocumentStyleGenerator {
  constructor(private theme: DocumentTheme) {}

  /**
   * Generate base document styles that are shared between preview and export
   */
  generateBaseStyles(): string {
    return `
      /* Base Typography */
      body {
        font-family: "${this.theme.fontFamily || 'Times New Roman'}", serif;
        font-size: ${this.theme.fontSize || '12pt'};
        line-height: ${this.theme.lineHeight || 1.6};
        color: ${this.theme.colors?.text || '#1a1a1a'};
        background: ${this.theme.colors?.background || '#ffffff'};
        margin: 0;
        padding: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }

      /* Document Container */
      .document-content {
        max-width: 100%;
        margin: 0 auto;
      }

      /* Headings */
      h1, h2, h3, h4, h5, h6 {
        font-family: "${this.theme.fontFamily || 'Times New Roman'}", serif;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        page-break-after: avoid;
        color: ${this.theme.colors?.text || '#1a1a1a'};
      }

      h1 { 
        font-size: 18pt; 
        margin-top: 0;
        text-align: center;
        margin-bottom: 1em;
      }
      h2 { 
        font-size: 16pt; 
        margin-top: 1.5em;
      }
      h3 { 
        font-size: 14pt; 
      }
      h4 { 
        font-size: 12pt; 
        font-weight: bold;
      }
      h5 { 
        font-size: 11pt; 
        font-weight: bold;
      }
      h6 { 
        font-size: 10pt; 
        font-weight: bold;
      }

      /* Paragraphs */
      p {
        margin-bottom: 1em;
        margin-top: 0;
        text-align: justify;
        orphans: 2;
        widows: 2;
      }

      /* Lists */
      ul, ol {
        margin: 1em 0;
        padding-left: 2em;
        page-break-inside: avoid;
      }

      li {
        margin-bottom: 0.5em;
        page-break-inside: avoid;
      }

      /* Tables */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 1em 0;
        page-break-inside: avoid;
        font-size: inherit;
      }

      th, td {
        border: 1px solid ${this.theme.colors?.text || '#1a1a1a'};
        padding: 8px 12px;
        text-align: left;
        vertical-align: top;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      /* Legal Document Specific Styles */
      .legal-clause {
        margin: 1.5em 0;
        padding: 0.5em 0;
        page-break-inside: avoid;
      }

      .signature-block {
        margin-top: 3em;
        page-break-inside: avoid;
        border-top: 2px solid ${this.theme.colors?.text || '#1a1a1a'};
        padding-top: 2em;
      }

      .signature-line {
        border-bottom: 1px solid ${this.theme.colors?.text || '#1a1a1a'};
        margin: 2em 0 0.5em 0;
        min-height: 1.5em;
      }

      /* Strong and Emphasis */
      strong, b {
        font-weight: bold;
      }

      em, i {
        font-style: italic;
      }

      /* Links */
      a {
        color: ${this.theme.colors?.accent || '#3b82f6'};
        text-decoration: underline;
      }

      /* Blockquotes */
      blockquote {
        margin: 1em 2em;
        padding-left: 1em;
        border-left: 3px solid ${this.theme.colors?.border || '#e5e7eb'};
        font-style: italic;
      }

      /* Code */
      code {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 0.9em;
      }

      pre {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 1em;
        border-radius: 5px;
        overflow-x: auto;
        white-space: pre-wrap;
      }

      /* Horizontal Rules */
      hr {
        border: none;
        border-top: 1px solid ${this.theme.colors?.border || '#e5e7eb'};
        margin: 2em 0;
      }
    `;
  }

  /**
   * Generate print-specific styles for export
   */
  generatePrintStyles(): string {
    return `
      @page {
        size: ${this.theme.pageWidth || '8.5in'} 11in;
        margin: ${this.theme.margins?.top || '1in'} ${this.theme.margins?.right || '1in'} 
                ${this.theme.margins?.bottom || '1in'} ${this.theme.margins?.left || '1in'};
      }

      /* Print-specific rules */
      @media print {
        body {
          print-color-adjust: exact;
          -webkit-print-color-adjust: exact;
        }

        h1, h2, h3, h4, h5, h6 {
          page-break-after: avoid;
        }

        p, li {
          orphans: 2;
          widows: 2;
        }

        table, blockquote, pre {
          page-break-inside: avoid;
        }

        img {
          max-width: 100%;
          page-break-inside: avoid;
        }
      }
    `;
  }

  /**
   * Generate preview-specific styles (includes container styling)
   */
  generatePreviewStyles(zoomLevel: number = 1): React.CSSProperties {
    return {
      fontFamily: `"${this.theme.fontFamily || 'Times New Roman'}", serif`,
      fontSize: this.theme.fontSize || '12pt',
      lineHeight: this.theme.lineHeight || 1.6,
      color: this.theme.colors?.text || '#1a1a1a',
      background: this.theme.colors?.background || '#ffffff',
      minHeight: '11in',
      padding: `${this.theme.margins?.top || '1in'} ${this.theme.margins?.right || '1in'} ${this.theme.margins?.bottom || '1in'} ${this.theme.margins?.left || '1in'}`,
      maxWidth: this.theme.pageWidth || '8.5in',
      margin: '0 auto',
      boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
      borderRadius: '4px',
      transform: `scale(${zoomLevel})`,
      transformOrigin: 'top center',
      transition: 'transform 0.2s ease-in-out',
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale',
      textRendering: 'optimizeLegibility'
    };
  }

  /**
   * Generate complete export HTML with unified styling
   */
  generateExportHTML(content: string, title: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>${title}</title>
          <style>
            ${this.generateBaseStyles()}
            ${this.generatePrintStyles()}
          </style>
        </head>
        <body>
          <div class="document-content">
            ${content}
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate print HTML with unified styling
   */
  generatePrintHTML(content: string, title: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <title>${title}</title>
          <style>
            ${this.generateBaseStyles()}
            ${this.generatePrintStyles()}
          </style>
        </head>
        <body>
          <div class="document-content">
            ${content}
          </div>
        </body>
      </html>
    `;
  }
}

export default DocumentStyleGenerator;
