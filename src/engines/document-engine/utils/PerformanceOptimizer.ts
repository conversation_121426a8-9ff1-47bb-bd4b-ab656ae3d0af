import { debounce, throttle } from 'lodash';

/**
 * Performance optimization utilities for the Document Engine
 */

// Debounced functions for performance optimization
export const createDebouncedUpdater = (
  updateFn: (content: string) => void,
  delay: number = 300
) => {
  return debounce(updateFn, delay, {
    leading: false,
    trailing: true
  });
};

export const createThrottledUpdater = (
  updateFn: (content: string) => void,
  delay: number = 100
) => {
  return throttle(updateFn, delay, {
    leading: true,
    trailing: true
  });
};

// Content change detection
export class ContentChangeDetector {
  private lastContent: string = '';
  private lastHash: string = '';

  constructor(private threshold: number = 0.1) {}

  hasSignificantChange(newContent: string): boolean {
    const newHash = this.generateContentHash(newContent);
    
    if (newHash === this.lastHash) {
      return false;
    }

    const changeRatio = this.calculateChangeRatio(this.lastContent, newContent);
    const hasSignificantChange = changeRatio > this.threshold;

    if (hasSignificantChange) {
      this.lastContent = newContent;
      this.lastHash = newHash;
    }

    return hasSignificantChange;
  }

  private generateContentHash(content: string): string {
    // Simple hash function for content comparison
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private calculateChangeRatio(oldContent: string, newContent: string): number {
    if (!oldContent && !newContent) return 0;
    if (!oldContent || !newContent) return 1;

    const maxLength = Math.max(oldContent.length, newContent.length);
    const minLength = Math.min(oldContent.length, newContent.length);
    
    let differences = Math.abs(oldContent.length - newContent.length);
    
    for (let i = 0; i < minLength; i++) {
      if (oldContent[i] !== newContent[i]) {
        differences++;
      }
    }

    return differences / maxLength;
  }

  reset(): void {
    this.lastContent = '';
    this.lastHash = '';
  }
}

// Memory management utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private cache = new Map<string, any>();
  private maxCacheSize = 100;

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  set(key: string, value: any): void {
    if (this.cache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  get(key: string): any {
    return this.cache.get(key);
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  clear(): void {
    this.cache.clear();
  }

  getSize(): number {
    return this.cache.size;
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>();
  private maxSamples = 100;

  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.recordMetric(operation, duration);
    };
  }

  recordMetric(operation: string, value: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }

    const samples = this.metrics.get(operation)!;
    samples.push(value);

    // Keep only the most recent samples
    if (samples.length > this.maxSamples) {
      samples.shift();
    }
  }

  getAverageTime(operation: string): number {
    const samples = this.metrics.get(operation);
    if (!samples || samples.length === 0) return 0;

    const sum = samples.reduce((acc, val) => acc + val, 0);
    return sum / samples.length;
  }

  getMetrics(): Record<string, { average: number; samples: number; latest: number }> {
    const result: Record<string, { average: number; samples: number; latest: number }> = {};

    for (const [operation, samples] of this.metrics.entries()) {
      result[operation] = {
        average: this.getAverageTime(operation),
        samples: samples.length,
        latest: samples[samples.length - 1] || 0
      };
    }

    return result;
  }

  clearMetrics(): void {
    this.metrics.clear();
  }

  // Check if performance is degrading
  isPerformanceDegrading(operation: string, threshold: number = 1000): boolean {
    const samples = this.metrics.get(operation);
    if (!samples || samples.length < 10) return false;

    const recentSamples = samples.slice(-5);
    const olderSamples = samples.slice(-15, -5);

    if (olderSamples.length === 0) return false;

    const recentAverage = recentSamples.reduce((acc, val) => acc + val, 0) / recentSamples.length;
    const olderAverage = olderSamples.reduce((acc, val) => acc + val, 0) / olderSamples.length;

    return recentAverage > olderAverage * 1.5 || recentAverage > threshold;
  }
}

// Lazy loading utilities
export class LazyLoader {
  private loadedModules = new Set<string>();
  private loadingPromises = new Map<string, Promise<any>>();

  async loadModule(moduleName: string, loader: () => Promise<any>): Promise<any> {
    if (this.loadedModules.has(moduleName)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(moduleName)) {
      return this.loadingPromises.get(moduleName);
    }

    const loadingPromise = loader().then((module) => {
      this.loadedModules.add(moduleName);
      this.loadingPromises.delete(moduleName);
      return module;
    }).catch((error) => {
      this.loadingPromises.delete(moduleName);
      throw error;
    });

    this.loadingPromises.set(moduleName, loadingPromise);
    return loadingPromise;
  }

  isLoaded(moduleName: string): boolean {
    return this.loadedModules.has(moduleName);
  }

  isLoading(moduleName: string): boolean {
    return this.loadingPromises.has(moduleName);
  }
}

// Batch operation utilities
export class BatchProcessor {
  private queue: Array<() => Promise<any>> = [];
  private processing = false;
  private batchSize = 5;
  private delay = 100;

  addOperation(operation: () => Promise<any>): void {
    this.queue.push(operation);
    this.processQueue();
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.batchSize);
      
      try {
        await Promise.all(batch.map(op => op()));
      } catch (error) {
        console.error('Batch processing error:', error);
      }

      // Small delay between batches to prevent blocking
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.delay));
      }
    }

    this.processing = false;
  }

  setBatchSize(size: number): void {
    this.batchSize = Math.max(1, size);
  }

  setDelay(delay: number): void {
    this.delay = Math.max(0, delay);
  }

  clear(): void {
    this.queue = [];
  }

  getQueueSize(): number {
    return this.queue.length;
  }
}

// Global performance optimizer instance
export const performanceOptimizer = {
  monitor: new PerformanceMonitor(),
  memory: MemoryManager.getInstance(),
  lazyLoader: new LazyLoader(),
  batchProcessor: new BatchProcessor(),
  
  // Utility functions
  createDebouncedUpdater,
  createThrottledUpdater,
  ContentChangeDetector
};

export default performanceOptimizer;
