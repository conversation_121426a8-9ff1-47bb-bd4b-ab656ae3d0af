import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Editor } from '@tiptap/react';
import {
  DocumentEngineConfig,
  DocumentStructure,
  DocumentEngineError,
  UseDocumentEngineReturn,
  DocumentMetadata,
  DocumentSection,
  OutlineNode,
  UserPresence,
  Comment
} from '../core/DocumentTypes';
import { performanceOptimizer, ContentChangeDetector } from '../utils/PerformanceOptimizer';

interface UseDocumentEngineProps {
  editor: Editor | null;
  config: DocumentEngineConfig;
  onError?: (error: DocumentEngineError) => void;
}

/**
 * Main hook for document engine functionality
 * Provides state management, document structure tracking, and collaboration features
 */
export function useDocumentEngine({
  editor,
  config,
  onError
}: UseDocumentEngineProps): UseDocumentEngineReturn {
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<DocumentEngineError | null>(null);
  const [content, setContent] = useState('');

  // Performance optimization refs
  const changeDetectorRef = useRef(new ContentChangeDetector());
  const lastUpdateTimeRef = useRef(Date.now());
  
  // Document structure state
  const [structure, setStructure] = useState<DocumentStructure>({
    sections: [],
    outline: [],
    metadata: {
      id: generateDocumentId(),
      title: 'Untitled Document',
      created: new Date(),
      modified: new Date(),
      version: '1.0.0',
      authors: [],
      tags: []
    }
  });

  // Collaboration state (placeholders for future implementation)
  const [collaborators, setCollaborators] = useState<UserPresence[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);

  // Generate unique document ID
  function generateDocumentId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Handle errors
  const handleError = useCallback((errorCode: string, message: string, details?: any) => {
    const error: DocumentEngineError = {
      code: errorCode,
      message,
      details,
      timestamp: new Date()
    };
    setError(error);
    onError?.(error);
  }, [onError]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Optimized document structure update with performance monitoring
  const updateDocumentStructure = useCallback(() => {
    if (!editor) return;

    const endTiming = performanceOptimizer.monitor.startTiming('updateDocumentStructure');

    try {
      const sections: DocumentSection[] = [];
      const outline: OutlineNode[] = [];
      const outlineStack: OutlineNode[] = [];

      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'heading') {
          const level = node.attrs.level || 1;
          const title = node.textContent;
          const numbering = node.attrs['data-section-number'] || '';
          const sectionId = `section-${pos}`;

          // Create section
          const section: DocumentSection = {
            id: sectionId,
            type: 'heading',
            level,
            numbering,
            title,
            content: node.textContent,
            metadata: {
              created: new Date(),
              modified: new Date(),
              author: 'current-user' // TODO: Get from auth context
            }
          };
          sections.push(section);

          // Create outline node
          const outlineNode: OutlineNode = {
            id: sectionId,
            title,
            level,
            numbering,
            children: [],
            sectionId
          };

          // Build hierarchical outline
          while (outlineStack.length > 0 && outlineStack[outlineStack.length - 1].level >= level) {
            outlineStack.pop();
          }

          if (outlineStack.length === 0) {
            outline.push(outlineNode);
          } else {
            outlineStack[outlineStack.length - 1].children.push(outlineNode);
          }

          outlineStack.push(outlineNode);
        }
      });

      setStructure(prev => ({
        ...prev,
        sections,
        outline,
        metadata: {
          ...prev.metadata,
          modified: new Date()
        }
      }));

      endTiming();
    } catch (err) {
      endTiming();
      handleError('STRUCTURE_UPDATE_ERROR', 'Failed to update document structure', err);
    }
  }, [editor, handleError]);

  // Set content programmatically
  const setContentProgrammatically = useCallback((newContent: string) => {
    if (!editor) return;
    
    try {
      editor.commands.setContent(newContent);
      setContent(newContent);
    } catch (err) {
      handleError('CONTENT_SET_ERROR', 'Failed to set content', err);
    }
  }, [editor, handleError]);

  // Export document
  const exportDocument = useCallback(async (options: any): Promise<Blob> => {
    if (!editor) {
      throw new Error('Editor not available');
    }

    setIsLoading(true);
    try {
      // This would be implemented by the DocumentExporter
      // For now, return a simple HTML blob
      const htmlContent = editor.getHTML();
      const blob = new Blob([htmlContent], { type: 'text/html' });
      return blob;
    } catch (err) {
      handleError('EXPORT_ERROR', 'Failed to export document', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [editor, handleError]);

  // Save document
  const save = useCallback(async () => {
    if (!editor) return;

    setIsLoading(true);
    try {
      // This would integrate with the backend API
      // For now, just update the modified timestamp
      setStructure(prev => ({
        ...prev,
        metadata: {
          ...prev.metadata,
          modified: new Date()
        }
      }));
    } catch (err) {
      handleError('SAVE_ERROR', 'Failed to save document', err);
    } finally {
      setIsLoading(false);
    }
  }, [editor, handleError]);

  // Undo/Redo operations
  const undo = useCallback(() => {
    if (editor?.can().undo()) {
      editor.chain().focus().undo().run();
    }
  }, [editor]);

  const redo = useCallback(() => {
    if (editor?.can().redo()) {
      editor.chain().focus().redo().run();
    }
  }, [editor]);

  // Collaboration functions (placeholders for future implementation)
  const addComment = useCallback((content: string, position: { from: number; to: number }) => {
    const comment: Comment = {
      id: `comment-${Date.now()}`,
      content,
      author: 'current-user', // TODO: Get from auth context
      created: new Date(),
      resolved: false,
      position,
      replies: []
    };
    
    setComments(prev => [...prev, comment]);
  }, []);

  const resolveComment = useCallback((commentId: string) => {
    setComments(prev => 
      prev.map(comment => 
        comment.id === commentId 
          ? { ...comment, resolved: true, modified: new Date() }
          : comment
      )
    );
  }, []);

  // Create debounced structure update for performance
  const debouncedStructureUpdate = useMemo(
    () => performanceOptimizer.createDebouncedUpdater(
      (_content: string) => updateDocumentStructure(),
      500
    ),
    [updateDocumentStructure]
  );

  // Initialize editor event listeners with performance optimizations
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      const endTiming = performanceOptimizer.monitor.startTiming('editorUpdate');

      try {
        const newContent = editor.getHTML();

        // Only update if content has significantly changed
        if (changeDetectorRef.current.hasSignificantChange(newContent)) {
          setContent(newContent);
          debouncedStructureUpdate(newContent);
          lastUpdateTimeRef.current = Date.now();
        }

        endTiming();
      } catch (error) {
        endTiming();
        handleError('UPDATE_HANDLER_ERROR', 'Failed to handle editor update', error);
      }
    };

    const handleSelectionUpdate = () => {
      // Handle selection changes for collaboration features
      // Throttled to prevent excessive updates
      const now = Date.now();
      if (now - lastUpdateTimeRef.current < 100) return;

      lastUpdateTimeRef.current = now;
    };

    const handleFocus = () => {
      clearError();
    };

    const handleBlur = () => {
      // Flush any pending updates when editor loses focus
      debouncedStructureUpdate.flush();
    };

    editor.on('update', handleUpdate);
    editor.on('selectionUpdate', handleSelectionUpdate);
    editor.on('focus', handleFocus);
    editor.on('blur', handleBlur);

    // Set initial content
    const initialContent = editor.getHTML();
    setContent(initialContent);
    changeDetectorRef.current.hasSignificantChange(initialContent);
    setIsReady(true);

    return () => {
      editor.off('update', handleUpdate);
      editor.off('selectionUpdate', handleSelectionUpdate);
      editor.off('focus', handleFocus);
      editor.off('blur', handleBlur);

      // Cancel any pending debounced calls
      debouncedStructureUpdate.cancel();
    };
  }, [editor, debouncedStructureUpdate, clearError, handleError]);

  // Update document structure when content changes
  useEffect(() => {
    if (editor && isReady) {
      updateDocumentStructure();
    }
  }, [editor, isReady, updateDocumentStructure]);

  // Memoized return value for performance
  return useMemo(() => ({
    editor,
    isReady,
    content,
    structure,
    isLoading,
    error,
    
    // Actions
    setContent: setContentProgrammatically,
    exportDocument,
    save,
    undo,
    redo,
    
    // Collaboration (future)
    collaborators,
    comments,
    addComment,
    resolveComment
  }), [
    editor,
    isReady,
    content,
    structure,
    isLoading,
    error,
    setContentProgrammatically,
    exportDocument,
    save,
    undo,
    redo,
    collaborators,
    comments,
    addComment,
    resolveComment
  ]);
}

export default useDocumentEngine;
