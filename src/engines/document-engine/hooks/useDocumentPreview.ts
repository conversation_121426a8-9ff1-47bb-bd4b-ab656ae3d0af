import { useState, useCallback, useMemo } from 'react';
import { DocumentTheme } from '../core/DocumentTypes';

export interface DocumentPreviewState {
  /** Whether preview modal is open */
  isOpen: boolean;
  /** Document content being previewed */
  content: string;
  /** Document title */
  title: string;
  /** Theme configuration */
  theme?: Partial<DocumentTheme>;
}

export interface UseDocumentPreviewReturn {
  /** Current preview state */
  previewState: DocumentPreviewState;
  /** Open preview modal with document */
  openPreview: (content: string, title?: string, theme?: Partial<DocumentTheme>) => void;
  /** Close preview modal */
  closePreview: () => void;
  /** Whether preview is currently open */
  isPreviewOpen: boolean;
}

/**
 * Hook for managing document preview state
 * 
 * Provides a simple interface for opening and closing document previews
 * across the application. Useful for:
 * - Template selection interfaces
 * - Document approval workflows
 * - Contract review processes
 * - Repository browsing
 */
export function useDocumentPreview(): UseDocumentPreviewReturn {
  const [previewState, setPreviewState] = useState<DocumentPreviewState>({
    isOpen: false,
    content: '',
    title: '',
    theme: undefined
  });

  const openPreview = useCallback((
    content: string, 
    title: string = 'Document Preview',
    theme?: Partial<DocumentTheme>
  ) => {
    setPreviewState({
      isOpen: true,
      content,
      title,
      theme
    });
  }, []);

  const closePreview = useCallback(() => {
    setPreviewState(prev => ({
      ...prev,
      isOpen: false
    }));
  }, []);

  const isPreviewOpen = useMemo(() => previewState.isOpen, [previewState.isOpen]);

  return {
    previewState,
    openPreview,
    closePreview,
    isPreviewOpen
  };
}

export default useDocumentPreview;
