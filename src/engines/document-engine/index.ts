// Document Engine - Main Export File
// This file exports all the public APIs of the document engine

import type { DocumentEngineConfig, ExportFormat } from './core/DocumentTypes';

// Core Components
export { DocumentEngine, type DocumentEngineProps } from './core/DocumentEngine';
export { default as DocumentToolbar } from './toolbar/DocumentToolbar';
export { default as DocumentExporter } from './export/DocumentExporter';
export { default as CollaborationManager } from './collaboration/CollaborationManager';

// Preview Components
export { DocumentPreviewEngine, type DocumentPreviewEngineProps } from './preview/DocumentPreviewEngine';
export { DocumentPreviewSimple, type DocumentPreviewSimpleProps } from './preview/DocumentPreviewSimple';
export { DocumentPreviewModal, type DocumentPreviewModalProps } from './preview/DocumentPreviewModal';

// UI Components
export {
  DocumentEngineErrorBoundary,
  useDocumentEngineErrorHandler,
  SimpleErrorFallback
} from './components/DocumentEngineErrorBoundary';
export {
  DocumentEngineLoader,
  DocumentContentSkeleton,
  DocumentToolbarSkeleton,
  DocumentEngineLoadingState
} from './components/DocumentEngineLoader';

// Hooks
export { useDocumentEngine, default as useDocumentEngineHook } from './hooks/useDocumentEngine';
export { useDocumentPreview } from './hooks/useDocumentPreview';

// Styling
export { DocumentStyleGenerator } from './styles/DocumentStyleGenerator';

// TipTap Extensions
export {
  createTipTapExtensions,
  // Temporarily disable custom extensions exports
  // AutoNumbering,
  // LegalClause,
  // CrossReference,
  // DocumentStructure
} from './editor/TipTapExtensions';

// Types and Interfaces
export type {
  // Core Types
  DocumentEngineConfig,
  DocumentEngineEvents,
  DocumentTheme,
  DocumentData,
  DocumentEngineError,
  UseDocumentEngineReturn,
  
  // Document Structure
  DocumentStructure as DocumentStructureType,
  DocumentSection,
  DocumentMetadata,
  OutlineNode,
  SectionMetadata,
  
  // Toolbar Types
  ToolbarConfig,
  ToolbarSection,
  ToolbarItem,
  CustomToolbarButton,
  
  // Export Types
  ExportConfig,
  ExportFormat,
  ExportOptions,
  
  // Collaboration Types (for future use)
  CollaborationConfig,
  DocumentPermissions,
  Permission,
  DocumentOperation,
  OperationMetadata,
  UserPresence,
  
  // Comment System Types
  Comment,
  CommentMetadata
} from './core/DocumentTypes';

// Utility Classes for Collaboration (placeholders)
export {
  OperationalTransform,
  ConflictResolver,
  WebSocketManager
} from './collaboration/CollaborationManager';

// Performance Utilities
export {
  performanceOptimizer,
  ContentChangeDetector,
  MemoryManager,
  PerformanceMonitor,
  LazyLoader,
  BatchProcessor
} from './utils/PerformanceOptimizer';

// Default Configurations
export const DEFAULT_DOCUMENT_THEME = {
  fontFamily: 'Times New Roman',
  fontSize: '12pt',
  lineHeight: 1.6,
  pageWidth: '8.5in',
  margins: {
    top: '1in',
    right: '1in',
    bottom: '1in',
    left: '1in'
  },
  colors: {
    text: '#1a1a1a',
    background: '#ffffff',
    border: '#e5e7eb',
    accent: '#3b82f6'
  }
};

export const DEFAULT_TOOLBAR_CONFIG = {
  simplified: false,
  sections: [
    {
      id: 'file',
      label: 'File',
      items: [
        { id: 'save', type: 'button' as const },
        { id: 'export', type: 'dropdown' as const }
      ]
    },
    {
      id: 'edit',
      label: 'Edit',
      items: [
        { id: 'undo', type: 'button' as const },
        { id: 'redo', type: 'button' as const }
      ]
    },
    {
      id: 'format',
      label: 'Format',
      items: [
        { id: 'bold', type: 'button' as const },
        { id: 'italic', type: 'button' as const },
        { id: 'underline', type: 'button' as const }
      ]
    }
  ],
  hiddenFeatures: []
};

export const DEFAULT_EXPORT_CONFIG = {
  formats: ['pdf', 'docx', 'html'] as ExportFormat[],
  defaultFormat: 'pdf' as ExportFormat,
  customOptions: {}
};

// Factory Functions for Easy Setup

/**
 * Create a document engine configuration for wizard mode
 */
export function createWizardConfig(overrides?: Partial<DocumentEngineConfig>): DocumentEngineConfig {
  return {
    mode: 'wizard',
    theme: DEFAULT_DOCUMENT_THEME,
    toolbar: {
      ...DEFAULT_TOOLBAR_CONFIG,
      simplified: true
    },
    export: DEFAULT_EXPORT_CONFIG,
    collaboration: {
      enabled: false
    },
    ...overrides
  };
}

/**
 * Create a document engine configuration for standalone mode
 */
export function createStandaloneConfig(overrides?: Partial<DocumentEngineConfig>): DocumentEngineConfig {
  return {
    mode: 'standalone',
    theme: DEFAULT_DOCUMENT_THEME,
    toolbar: DEFAULT_TOOLBAR_CONFIG,
    export: DEFAULT_EXPORT_CONFIG,
    collaboration: {
      enabled: false
    },
    ...overrides
  };
}

/**
 * Create a document engine configuration for collaborative mode
 */
export function createCollaborativeConfig(
  roomId: string, 
  overrides?: Partial<DocumentEngineConfig>
): DocumentEngineConfig {
  return {
    mode: 'collaborative',
    theme: DEFAULT_DOCUMENT_THEME,
    toolbar: DEFAULT_TOOLBAR_CONFIG,
    export: DEFAULT_EXPORT_CONFIG,
    collaboration: {
      enabled: true,
      roomId,
      websocketUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
      conflictResolution: 'automatic'
    },
    ...overrides
  };
}

// Version Information
export const DOCUMENT_ENGINE_VERSION = '1.0.0';
export const SUPPORTED_FORMATS = ['pdf', 'docx', 'html', 'txt', 'json'] as const;
export const SUPPORTED_MODES = ['wizard', 'standalone', 'collaborative'] as const;

// Feature Flags (for gradual rollout)
export const FEATURE_FLAGS = {
  COLLABORATION: false, // Enable when WebSocket integration is ready
  ADVANCED_EXPORT: true,
  AUTO_NUMBERING: true,
  LEGAL_CLAUSES: true,
  CROSS_REFERENCES: false, // Enable when fully implemented
  COMMENTS: false, // Enable when comment system is ready
  TRACK_CHANGES: false, // Future feature
  VERSION_CONTROL: false // Future feature
};

// Error Codes
export const ERROR_CODES = {
  EDITOR_NOT_READY: 'EDITOR_NOT_READY',
  EXPORT_FAILED: 'EXPORT_FAILED',
  SAVE_FAILED: 'SAVE_FAILED',
  COLLABORATION_ERROR: 'COLLABORATION_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INVALID_CONFIG: 'INVALID_CONFIG',
  NETWORK_ERROR: 'NETWORK_ERROR'
} as const;

// Helper Functions

/**
 * Validate document engine configuration
 */
export function validateConfig(config: DocumentEngineConfig): boolean {
  if (!SUPPORTED_MODES.includes(config.mode)) {
    console.error(`Invalid mode: ${config.mode}. Supported modes: ${SUPPORTED_MODES.join(', ')}`);
    return false;
  }

  if (config.collaboration?.enabled && !config.collaboration.roomId) {
    console.error('Room ID is required when collaboration is enabled');
    return false;
  }

  return true;
}

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
  return FEATURE_FLAGS[feature];
}

/**
 * Get supported export formats
 */
export function getSupportedFormats(): readonly string[] {
  return SUPPORTED_FORMATS;
}

/**
 * Create error object
 */
export function createDocumentEngineError(
  code: keyof typeof ERROR_CODES,
  message: string,
  details?: any
) {
  return {
    code: ERROR_CODES[code],
    message,
    details,
    timestamp: new Date()
  };
}
