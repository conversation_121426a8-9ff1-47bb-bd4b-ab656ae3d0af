import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Eye, Download, Printer, FileText } from 'lucide-react';
import { 
  DocumentPreviewEngine, 
  DocumentPreviewSimple,
  DocumentStyleGenerator,
  DocumentTheme
} from '@/engines/document-engine';

const testContent = `
  <h1>Document Styling Consistency Test</h1>
  
  <p>This document tests the consistency between preview and export styling. All elements should appear identical in both preview and exported versions.</p>
  
  <h2>Typography Test</h2>
  <p>This is a regular paragraph with <strong>bold text</strong>, <em>italic text</em>, and <a href="#">linked text</a>.</p>
  
  <h3>Heading Level 3</h3>
  <p>Paragraph following a level 3 heading with proper spacing and margins.</p>
  
  <h4>Heading Level 4</h4>
  <p>Another paragraph to test heading hierarchy and spacing consistency.</p>
  
  <h2>Lists Test</h2>
  <p>Unordered list:</p>
  <ul>
    <li>First list item with sufficient spacing</li>
    <li>Second list item with proper indentation</li>
    <li>Third list item to test consistency</li>
  </ul>
  
  <p>Ordered list:</p>
  <ol>
    <li>First numbered item</li>
    <li>Second numbered item</li>
    <li>Third numbered item</li>
  </ol>
  
  <h2>Table Test</h2>
  <table>
    <thead>
      <tr>
        <th>Column 1</th>
        <th>Column 2</th>
        <th>Column 3</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Row 1, Cell 1</td>
        <td>Row 1, Cell 2</td>
        <td>Row 1, Cell 3</td>
      </tr>
      <tr>
        <td>Row 2, Cell 1</td>
        <td>Row 2, Cell 2</td>
        <td>Row 2, Cell 3</td>
      </tr>
    </tbody>
  </table>
  
  <h2>Legal Document Elements</h2>
  <div class="legal-clause">
    <p><strong>WHEREAS,</strong> the parties wish to enter into this agreement;</p>
  </div>
  
  <div class="legal-clause">
    <p><strong>NOW, THEREFORE,</strong> in consideration of the mutual covenants contained herein;</p>
  </div>
  
  <div class="signature-block">
    <h3>SIGNATURES</h3>
    <div style="display: flex; justify-content: space-between; margin-top: 2em;">
      <div style="width: 45%;">
        <div class="signature-line"></div>
        <p>Party A Signature</p>
        <p>Date: _______________</p>
      </div>
      <div style="width: 45%;">
        <div class="signature-line"></div>
        <p>Party B Signature</p>
        <p>Date: _______________</p>
      </div>
    </div>
  </div>
  
  <hr />
  
  <p style="text-align: center; font-size: 0.9em; color: #666;">
    Document generated on ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
  </p>
`;

const testTheme: DocumentTheme = {
  fontFamily: 'Times New Roman',
  fontSize: '12pt',
  lineHeight: 1.6,
  pageWidth: '8.5in',
  margins: {
    top: '1in',
    right: '1in',
    bottom: '1in',
    left: '1in'
  },
  colors: {
    text: '#1a1a1a',
    background: '#ffffff',
    border: '#e5e7eb',
    accent: '#3b82f6'
  }
};

/**
 * DocumentStylingTest - Component to test styling consistency
 * 
 * This component allows developers to verify that the preview engine
 * and export functionality produce visually identical results.
 */
export const DocumentStylingTest: React.FC = () => {
  const [selectedView, setSelectedView] = useState<'preview' | 'simple'>('preview');

  const handleExportHTML = () => {
    const styleGenerator = new DocumentStyleGenerator(testTheme);
    const htmlContent = styleGenerator.generateExportHTML(testContent, 'Styling Test Document');
    
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'styling_test_export.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handlePrintTest = () => {
    const styleGenerator = new DocumentStyleGenerator(testTheme);
    const printHTML = styleGenerator.generatePrintHTML(testContent, 'Styling Test Document');
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="document-styling-test p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Document Styling Consistency Test</h1>
        <p className="text-muted-foreground">
          Verify that preview and export styling are identical (WYSIWYG)
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
          <CardDescription>
            Follow these steps to verify styling consistency:
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Badge variant="outline" className="w-full justify-center">Step 1</Badge>
              <p className="text-sm">Review the document preview below and note the styling</p>
            </div>
            <div className="space-y-2">
              <Badge variant="outline" className="w-full justify-center">Step 2</Badge>
              <p className="text-sm">Export or print the document using the buttons</p>
            </div>
            <div className="space-y-2">
              <Badge variant="outline" className="w-full justify-center">Step 3</Badge>
              <p className="text-sm">Compare the exported version with the preview</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2 pt-4 border-t">
            <Button onClick={handleExportHTML} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export HTML
            </Button>
            <Button onClick={handlePrintTest} variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              Print Test
            </Button>
            <div className="ml-auto flex items-center gap-2">
              <Button
                onClick={() => setSelectedView('preview')}
                variant={selectedView === 'preview' ? 'default' : 'outline'}
                size="sm"
              >
                <Eye className="h-4 w-4 mr-2" />
                Full Preview
              </Button>
              <Button
                onClick={() => setSelectedView('simple')}
                variant={selectedView === 'simple' ? 'default' : 'outline'}
                size="sm"
              >
                <FileText className="h-4 w-4 mr-2" />
                Simple Preview
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Document Preview</CardTitle>
          <CardDescription>
            This preview should match exactly with exported documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {selectedView === 'preview' ? (
            <div className="h-[800px] border rounded-lg overflow-hidden">
              <DocumentPreviewEngine
                content={testContent}
                theme={testTheme}
                documentTitle="Styling Test Document"
                showZoomControls={true}
                showPrintButton={true}
                showDownloadButton={true}
                showFullscreenButton={true}
              />
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <DocumentPreviewSimple
                content={testContent}
                theme={testTheme}
                maxHeight="800px"
                scale={0.8}
                showShadow={true}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Expected Results</CardTitle>
          <CardDescription>
            The exported document should match the preview exactly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-green-600">✓ Consistent Elements</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Font family and sizes</li>
                <li>• Heading hierarchy and spacing</li>
                <li>• Paragraph margins and alignment</li>
                <li>• List indentation and spacing</li>
                <li>• Table borders and cell padding</li>
                <li>• Legal document formatting</li>
                <li>• Signature block layout</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-red-600">✗ Potential Issues</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Different font rendering</li>
                <li>• Inconsistent margins/padding</li>
                <li>• Missing typography styles</li>
                <li>• Table styling differences</li>
                <li>• Color variations</li>
                <li>• Line height discrepancies</li>
                <li>• Page break behavior</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentStylingTest;
