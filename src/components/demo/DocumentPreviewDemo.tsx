import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Eye, FileText, Maximize2 } from 'lucide-react';
import { 
  DocumentPreviewEngine, 
  DocumentPreviewSimple, 
  DocumentPreviewModal,
  useDocumentPreview 
} from '@/engines/document-engine';

const sampleContract = `
  <h1 style="text-align: center; margin-bottom: 2em;">SOFTWARE DEVELOPMENT AGREEMENT</h1>
  
  <div style="margin-bottom: 2em;">
    <p><strong>Contract No.:</strong> CONT-12345-2024</p>
    <p><strong>Effective Date:</strong> January 15, 2024</p>
    <p><strong>Parties:</strong></p>
    <ul>
      <li><strong>Client:</strong> TechCorp Solutions Inc.</li>
      <li><strong>Developer:</strong> Software Innovations LLC</li>
    </ul>
  </div>

  <h2>1. PROJECT SCOPE</h2>
  <p>The Developer agrees to design, develop, and deliver a custom web application according to the specifications outlined in Exhibit A, which is incorporated herein by reference.</p>

  <h2>2. DELIVERABLES</h2>
  <p>The project deliverables include:</p>
  <ul>
    <li>Complete source code and documentation</li>
    <li>User interface design and implementation</li>
    <li>Database design and setup</li>
    <li>Testing and quality assurance</li>
    <li>Deployment and configuration</li>
  </ul>

  <h2>3. PAYMENT TERMS</h2>
  <p><strong>Total Contract Value:</strong> $150,000.00</p>
  <p><strong>Payment Schedule:</strong></p>
  <ul>
    <li>30% ($45,000) upon contract signing</li>
    <li>40% ($60,000) upon milestone completion</li>
    <li>30% ($45,000) upon final delivery and acceptance</li>
  </ul>

  <h2>4. TIMELINE</h2>
  <p>The project shall be completed within six (6) months from the effective date, with the following milestones:</p>
  <ul>
    <li><strong>Month 1-2:</strong> Requirements analysis and design</li>
    <li><strong>Month 3-4:</strong> Development and implementation</li>
    <li><strong>Month 5:</strong> Testing and quality assurance</li>
    <li><strong>Month 6:</strong> Deployment and final delivery</li>
  </ul>

  <h2>5. INTELLECTUAL PROPERTY</h2>
  <p>Upon final payment, all intellectual property rights, including but not limited to copyrights, patents, and trade secrets related to the developed software, shall be transferred to the Client.</p>

  <h2>6. CONFIDENTIALITY</h2>
  <p>Both parties agree to maintain the confidentiality of all proprietary information shared during the course of this project.</p>

  <h2>7. TERMINATION</h2>
  <p>Either party may terminate this agreement with thirty (30) days written notice. In the event of termination, the Client shall pay for all work completed up to the termination date.</p>

  <div style="margin-top: 3em; border-top: 2px solid #333; padding-top: 2em;">
    <h2>SIGNATURES</h2>
    <div style="display: flex; justify-content: space-between; margin-top: 2em;">
      <div style="width: 45%;">
        <p><strong>CLIENT:</strong></p>
        <p>_________________________</p>
        <p>John Smith, CEO</p>
        <p>TechCorp Solutions Inc.</p>
        <p>Date: _______________</p>
      </div>
      <div style="width: 45%;">
        <p><strong>DEVELOPER:</strong></p>
        <p>_________________________</p>
        <p>Jane Doe, CTO</p>
        <p>Software Innovations LLC</p>
        <p>Date: _______________</p>
      </div>
    </div>
  </div>

  <div style="margin-top: 2em; text-align: center; font-size: 0.9em; color: #666;">
    <p>Contract executed on ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
  </div>
`;

const sampleTemplate = `
  <h1 style="text-align: center;">NON-DISCLOSURE AGREEMENT TEMPLATE</h1>
  <p style="text-align: center; font-style: italic;">Standard NDA for Business Discussions</p>
  
  <h2>1. PARTIES</h2>
  <p>This Non-Disclosure Agreement is entered into between [COMPANY_NAME] and [COUNTERPARTY_NAME].</p>
  
  <h2>2. CONFIDENTIAL INFORMATION</h2>
  <p>For purposes of this Agreement, "Confidential Information" includes all information disclosed by either party.</p>
  
  <h2>3. OBLIGATIONS</h2>
  <p>Each party agrees to maintain confidentiality and not disclose information to third parties.</p>
`;

/**
 * DocumentPreviewDemo - Demonstration component for document preview engines
 * 
 * Shows different use cases and configurations of the preview components:
 * - Full-featured preview engine
 * - Simple embedded previews
 * - Modal previews
 * - Different themes and configurations
 */
export const DocumentPreviewDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<string>('full');
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  const handleOpenModal = (content: string, title: string) => {
    openPreview(content, title);
  };

  return (
    <div className="document-preview-demo p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Document Preview Engine Demo</h1>
        <p className="text-muted-foreground">
          Explore different configurations and use cases for the document preview components
        </p>
      </div>

      <Tabs value={selectedDemo} onValueChange={setSelectedDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="full">Full Engine</TabsTrigger>
          <TabsTrigger value="simple">Simple Preview</TabsTrigger>
          <TabsTrigger value="modal">Modal Previews</TabsTrigger>
          <TabsTrigger value="workflow">Approval Workflow</TabsTrigger>
        </TabsList>

        <TabsContent value="full" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Full Document Preview Engine</CardTitle>
              <CardDescription>
                Complete preview engine with zoom controls, print, download, and fullscreen capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[600px] border rounded-lg overflow-hidden">
                <DocumentPreviewEngine
                  content={sampleContract}
                  documentTitle="Software Development Agreement"
                  showZoomControls={true}
                  showPrintButton={true}
                  showDownloadButton={true}
                  showFullscreenButton={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="simple" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Scaled Preview (80%)</CardTitle>
                <CardDescription>Compact preview for cards and lists</CardDescription>
              </CardHeader>
              <CardContent>
                <DocumentPreviewSimple
                  content={sampleContract}
                  maxHeight="400px"
                  scale={0.6}
                  showShadow={true}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Template Preview</CardTitle>
                <CardDescription>Simple template preview without shadow</CardDescription>
              </CardHeader>
              <CardContent>
                <DocumentPreviewSimple
                  content={sampleTemplate}
                  maxHeight="400px"
                  scale={0.7}
                  showShadow={false}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="modal" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Contract Preview
                </CardTitle>
                <CardDescription>Software Development Agreement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="secondary">Service Agreement</Badge>
                  <p className="text-sm text-muted-foreground">
                    Complete software development contract with payment terms and deliverables.
                  </p>
                  <Button 
                    onClick={() => handleOpenModal(sampleContract, 'Software Development Agreement')}
                    className="w-full"
                    variant="outline"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Contract
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Template Preview
                </CardTitle>
                <CardDescription>NDA Template</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="secondary">Template</Badge>
                  <p className="text-sm text-muted-foreground">
                    Standard non-disclosure agreement template for business discussions.
                  </p>
                  <Button 
                    onClick={() => handleOpenModal(sampleTemplate, 'NDA Template')}
                    className="w-full"
                    variant="outline"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Template
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Maximize2 className="h-5 w-5" />
                  Fullscreen Demo
                </CardTitle>
                <CardDescription>Large Modal Preview</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="secondary">Full Size</Badge>
                  <p className="text-sm text-muted-foreground">
                    Open document in a large modal with all preview features enabled.
                  </p>
                  <Button 
                    onClick={() => handleOpenModal(sampleContract, 'Fullscreen Contract Preview')}
                    className="w-full"
                    variant="outline"
                  >
                    <Maximize2 className="h-4 w-4 mr-2" />
                    Open Fullscreen
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workflow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Approval Workflow</CardTitle>
              <CardDescription>
                Example of how the preview engine integrates with approval workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  The ContractApprovalWorkflow component demonstrates how the preview engine
                  can be integrated into business processes for document review and approval.
                </p>
                <Button variant="outline">
                  View Approval Workflow Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default DocumentPreviewDemo;
