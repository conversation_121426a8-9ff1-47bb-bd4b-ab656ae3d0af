import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Clock, Eye, MessageSquare, User } from 'lucide-react';
import { DocumentPreviewModal } from '@/engines/document-engine/preview/DocumentPreviewModal';
import { useDocumentPreview } from '@/engines/document-engine/hooks/useDocumentPreview';

interface ApprovalRequest {
  id: string;
  contractTitle: string;
  contractType: string;
  requestedBy: {
    name: string;
    email: string;
  };
  requestedDate: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'approved' | 'rejected' | 'requires_changes';
  description: string;
  contractContent: string;
  comments?: string;
}

interface ContractApprovalWorkflowProps {
  /** List of approval requests */
  approvalRequests?: ApprovalRequest[];
  /** Callback when approval action is taken */
  onApprovalAction?: (requestId: string, action: 'approve' | 'reject' | 'request_changes', comments?: string) => void;
}

const mockApprovalRequests: ApprovalRequest[] = [
  {
    id: '1',
    contractTitle: 'Software Development Agreement - TechCorp',
    contractType: 'Service Agreement',
    requestedBy: {
      name: 'John Smith',
      email: '<EMAIL>'
    },
    requestedDate: '2024-01-15',
    priority: 'high',
    status: 'pending',
    description: 'New software development contract for Q1 2024 project with TechCorp. Includes milestone-based payments and IP ownership clauses.',
    contractContent: `
      <h1>SOFTWARE DEVELOPMENT AGREEMENT</h1>
      <p><strong>Parties:</strong> Company Inc. and TechCorp Solutions</p>
      <p><strong>Effective Date:</strong> January 15, 2024</p>
      <p><strong>Project Scope:</strong> Development of custom software application</p>
      <h2>1. SCOPE OF WORK</h2>
      <p>TechCorp will develop a custom software application according to the specifications outlined in Exhibit A.</p>
      <h2>2. PAYMENT TERMS</h2>
      <p>Total contract value: $150,000</p>
      <p>Payment schedule: 30% upfront, 40% at milestone completion, 30% upon delivery</p>
      <h2>3. INTELLECTUAL PROPERTY</h2>
      <p>All intellectual property rights will be transferred to Company Inc. upon final payment.</p>
      <h2>4. TIMELINE</h2>
      <p>Project completion: 6 months from effective date</p>
    `
  },
  {
    id: '2',
    contractTitle: 'Non-Disclosure Agreement - StartupXYZ',
    contractType: 'NDA',
    requestedBy: {
      name: 'Sarah Johnson',
      email: '<EMAIL>'
    },
    requestedDate: '2024-01-14',
    priority: 'medium',
    status: 'pending',
    description: 'Standard NDA for potential partnership discussions with StartupXYZ.',
    contractContent: `
      <h1>NON-DISCLOSURE AGREEMENT</h1>
      <p><strong>Parties:</strong> Company Inc. and StartupXYZ</p>
      <p><strong>Effective Date:</strong> January 14, 2024</p>
      <h2>1. CONFIDENTIAL INFORMATION</h2>
      <p>Both parties agree to maintain confidentiality of all proprietary information shared during partnership discussions.</p>
      <h2>2. TERM</h2>
      <p>This agreement shall remain in effect for 2 years from the effective date.</p>
      <h2>3. OBLIGATIONS</h2>
      <p>Each party agrees not to disclose confidential information to third parties without written consent.</p>
    `
  }
];

/**
 * ContractApprovalWorkflow - Component for managing contract approval processes
 * 
 * Features:
 * - List of pending approval requests
 * - Document preview integration
 * - Approval/rejection actions with comments
 * - Priority-based sorting
 * - Status tracking
 */
export const ContractApprovalWorkflow: React.FC<ContractApprovalWorkflowProps> = ({
  approvalRequests = mockApprovalRequests,
  onApprovalAction
}) => {
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [comments, setComments] = useState<{ [key: string]: string }>({});
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  const handlePreviewContract = (request: ApprovalRequest) => {
    openPreview(request.contractContent, `${request.contractTitle} - Contract Preview`);
  };

  const handleApprovalAction = (requestId: string, action: 'approve' | 'reject' | 'request_changes') => {
    const comment = comments[requestId] || '';
    onApprovalAction?.(requestId, action, comment);
    setComments(prev => ({ ...prev, [requestId]: '' }));
    setSelectedRequest(null);
  };

  const getPriorityColor = (priority: ApprovalRequest['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: ApprovalRequest['status']) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'requires_changes': return <MessageSquare className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="contract-approval-workflow space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Contract Approval Workflow</h2>
          <p className="text-muted-foreground">Review and approve pending contract requests</p>
        </div>
        <Badge variant="secondary">
          {approvalRequests.filter(r => r.status === 'pending').length} Pending
        </Badge>
      </div>

      <div className="space-y-4">
        {approvalRequests.map((request) => (
          <Card key={request.id} className="w-full">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{request.contractTitle}</CardTitle>
                  <CardDescription>{request.description}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={`${getPriorityColor(request.priority)} text-white`}>
                    {request.priority.toUpperCase()}
                  </Badge>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(request.status)}
                    <span className="text-sm capitalize">{request.status.replace('_', ' ')}</span>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <Label className="text-muted-foreground">Type</Label>
                  <p className="font-medium">{request.contractType}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Requested By</Label>
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span className="font-medium">{request.requestedBy.name}</span>
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Date</Label>
                  <p className="font-medium">{new Date(request.requestedDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Status</Label>
                  <p className="font-medium capitalize">{request.status.replace('_', ' ')}</p>
                </div>
              </div>

              {request.status === 'pending' && (
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewContract(request)}
                      className="flex items-center gap-1"
                    >
                      <Eye className="h-4 w-4" />
                      Preview Contract
                    </Button>
                  </div>

                  {selectedRequest === request.id && (
                    <div className="space-y-3 p-4 bg-muted rounded-lg">
                      <Label htmlFor={`comments-${request.id}`}>Comments (optional)</Label>
                      <Textarea
                        id={`comments-${request.id}`}
                        placeholder="Add any comments or feedback..."
                        value={comments[request.id] || ''}
                        onChange={(e) => setComments(prev => ({ ...prev, [request.id]: e.target.value }))}
                        className="min-h-[80px]"
                      />
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleApprovalAction(request.id, 'approve')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleApprovalAction(request.id, 'request_changes')}
                        >
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Request Changes
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleApprovalAction(request.id, 'reject')}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setSelectedRequest(null)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}

                  {selectedRequest !== request.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedRequest(request.id)}
                    >
                      Review & Take Action
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default ContractApprovalWorkflow;
