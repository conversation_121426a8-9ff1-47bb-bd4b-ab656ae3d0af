import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, ArrowRight, Search, Clock, Star, CheckCircle, Users, AlertCircle, Code, Building, GraduationCap, Home, ShoppingBag, Factory, MoreHorizontal, Loader2 } from "lucide-react";
import { Template, templateStore } from "@/types/template";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApi } from "@/lib/api";
import { TemplateService } from "@/services/api-services";
import type { Template as ApiTemplate } from "@/services/api-types";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { DocumentPreviewModal } from "@/engines/document-engine/preview/DocumentPreviewModal";
import { useDocumentPreview } from "@/engines/document-engine/hooks/useDocumentPreview";

interface ContractTemplatesProps {
  onSelectTemplate: (templateId: string) => void;
}

const ContractTemplates = ({ onSelectTemplate }: ContractTemplatesProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { fetch, fetchArray } = useApi();
  const { currentWorkspace } = useClerkWorkspace();
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  // Load templates from store and API
  useEffect(() => {
    const fetchTemplates = async () => {
      setLoading(true);
      setError(null);

      try {
        // Load templates from localStorage first
        templateStore.loadFromLocalStorage();
        const localTemplates = templateStore.getTemplates();
        setTemplates(localTemplates);

        // Then fetch templates from API
        // Only fetch templates if we have a workspace
        if (!currentWorkspace) {
          setLoading(false);
          return;
        }

        const params = {
          workspace_id: currentWorkspace.id,
        };

        const result = await fetchArray(
          () => TemplateService.getTemplates(params),
          "Loading templates...",
          "Failed to load templates"
        );

        if (result && result.length > 0) {
          // Map API templates to UI templates and merge with local templates
          const apiTemplates = result.map((template: ApiTemplate) => ({
            id: template.id,
            title: template.title,
            description: template.description,
            type: template.type,
            complexity: template.complexity,
            industry: template.industry || '',
            tags: template.tags || [],
            icon: template.icon || '',
            lastUpdated: template.updated_at || template.created_at,
            usageCount: template.usage_count,
            isUserCreated: template.is_user_created,
            folderId: template.folder_id || 'folder-4', // Default to templates folder
          }));

          // Merge with local templates
          const mergedTemplates = [...localTemplates];

          // Add API templates that don't exist locally
          apiTemplates.forEach(apiTemplate => {
            if (!localTemplates.some(localTemplate => localTemplate.id === apiTemplate.id)) {
              mergedTemplates.push(apiTemplate);
            }
          });

          setTemplates(mergedTemplates);

          // Update local storage with merged templates
          templateStore.setTemplates(mergedTemplates);
          templateStore.saveToLocalStorage();
        }
      } catch (err) {
        console.error("Error fetching templates:", err);
        setError("Failed to load templates. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, [currentWorkspace, fetch]);

  // Filter templates based on search query and category
  useEffect(() => {
    let filtered = templates;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        template =>
          template.title.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query) ||
          template.type.toLowerCase().includes(query) ||
          template.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (activeCategory !== "all") {
      filtered = filtered.filter(template => {
        if (activeCategory === "simple") return template.complexity === "simple";
        if (activeCategory === "business") return template.tags?.includes("B2B") || template.industry === "Professional Services";
        if (activeCategory === "technology") return template.industry === "Technology" || template.tags?.includes("Technology");
        if (activeCategory === "employment") return template.type === "Employment" || template.tags?.includes("HR");
        return true;
      });
    }

    setFilteredTemplates(filtered);
  }, [searchQuery, activeCategory]);

  const getComplexityBadge = (complexity: Template["complexity"]) => {
    switch (complexity) {
      case "simple":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">Simple</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">Medium</Badge>;
      case "complex":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-xs">Complex</Badge>;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const renderRatingStars = (rating: number = 0) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-2.5 w-2.5 ${star <= rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
          />
        ))}
        <span className="text-xs ml-1 text-muted-foreground">{rating.toFixed(1)}</span>
      </div>
    );
  };

  const getTemplateIcon = (template: Template) => {
    switch (template.icon) {
      case "service":
        return <Users className="h-5 w-5 text-blue-600" />;
      case "nda":
        return <FileText className="h-5 w-5 text-purple-600" />;
      case "employment":
        return <GraduationCap className="h-5 w-5 text-green-600" />;
      case "software":
        return <Code className="h-5 w-5 text-cyan-600" />;
      case "lease":
        return <Home className="h-5 w-5 text-amber-600" />;
      case "manufacturing":
        return <Factory className="h-5 w-5 text-orange-600" />;
      case "sales":
        return <ShoppingBag className="h-5 w-5 text-pink-600" />;
      case "blank":
        return <FileText className="h-5 w-5 text-gray-600" />;
      default:
        return <FileText className="h-5 w-5 text-primary" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 border-b pb-3">
        <div>
          <h2 className="text-base font-medium">Available Templates</h2>
          <p className="text-xs text-muted-foreground mt-1">
            Choose a template to get started with your new contract
          </p>
        </div>

        <div className="w-full md:w-auto relative">
          <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="w-full md:w-[220px] pl-8 h-8 text-xs"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading templates...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch available templates
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading templates</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : (
        <Tabs defaultValue="all" onValueChange={setActiveCategory}>
          <TabsList className="mb-4">
            <TabsTrigger value="all" className="text-xs">All Templates</TabsTrigger>
            <TabsTrigger value="simple" className="text-xs">Simple</TabsTrigger>
            <TabsTrigger value="business" className="text-xs">Business</TabsTrigger>
            <TabsTrigger value="technology" className="text-xs">Technology</TabsTrigger>
            <TabsTrigger value="employment" className="text-xs">Employment</TabsTrigger>
          </TabsList>

        <TabsContent value="all" className="mt-0">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-8 bg-muted/30 rounded-lg">
              <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                <Search className="h-6 w-6 text-muted-foreground/60" />
              </div>
              <h3 className="text-sm font-medium mb-1.5">No templates found</h3>
              <p className="text-sm text-muted-foreground max-w-md mx-auto">
                We couldn't find any templates matching your search criteria. Try adjusting your search or browse all templates.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3 h-9 text-sm"
                onClick={() => {
                  setSearchQuery("");
                  setActiveCategory("all");
                }}
              >
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTemplates.map((template) => (
                <Card
                  key={template.id}
                  className="overflow-hidden border hover:border-primary/50 transition-all"
                >
                  <CardHeader className="pb-2 pt-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-sm font-medium">{template.title}</CardTitle>
                        <CardDescription className="line-clamp-2 text-xs mt-1">
                          {template.description}
                        </CardDescription>
                      </div>
                      <div className="flex items-center">
                        {getTemplateIcon(template)}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pb-2 space-y-2">
                    <div className="flex items-center gap-2">
                      {getComplexityBadge(template.complexity)}
                      {template.industry && (
                        <Badge variant="outline" className="text-xs">
                          {template.industry}
                        </Badge>
                      )}
                    </div>

                    {template.tags && template.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {template.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs py-0 px-1.5 font-normal">
                            {tag}
                          </Badge>
                        ))}
                        {template.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs py-0 px-1.5 font-normal">
                            +{template.tags.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(template.lastUpdated)}
                      </div>
                      <span>Used {template.usageCount} times</span>
                    </div>
                  </CardContent>

                  <CardFooter className="pt-0 pb-4 flex justify-between">
                    <div className="text-xs text-muted-foreground">
                      Type: {template.type}
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 text-xs"
                        onClick={() => onSelectTemplate(template.id)}
                      >
                        Use Template
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openPreview(template.content || '', `${template.name} - Preview`)}>
                            Preview Template
                          </DropdownMenuItem>
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Duplicate</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="simple" className="mt-0">
          {/* Content automatically filtered by useEffect */}
        </TabsContent>

        <TabsContent value="business" className="mt-0">
          {/* Content automatically filtered by useEffect */}
        </TabsContent>

        <TabsContent value="technology" className="mt-0">
          {/* Content automatically filtered by useEffect */}
        </TabsContent>

        <TabsContent value="employment" className="mt-0">
          {/* Content automatically filtered by useEffect */}
        </TabsContent>
      </Tabs>
      )}

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default ContractTemplates;
