import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap, 
  Shield, 
  FileText,
  Search,
  Lightbulb,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface AIAnalysisResult {
  id: string;
  type: 'enhancement' | 'optimization' | 'risk' | 'compliance';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  section?: string;
  suggestion?: string;
}

interface RiskScore {
  overall: number;
  legal: number;
  financial: number;
  operational: number;
}

const AIAnalysisInterface: React.FC = () => {
  const [activeTab, setActiveTab] = useState('analysis');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [customQuery, setCustomQuery] = useState('');
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResult[]>([]);
  const [riskScores, setRiskScores] = useState<RiskScore>({
    overall: 85,
    legal: 90,
    financial: 75,
    operational: 88
  });

  // Mock analysis results based on the screenshot design
  useEffect(() => {
    setAnalysisResults([
      {
        id: '1',
        type: 'enhancement',
        title: 'AI Enhancement Applied',
        description: 'Added specific deliverables reference for legal clarity',
        confidence: 95,
        impact: 'medium',
        section: 'SCOPE OF SERVICES',
        suggestion: 'Consider adding milestone-based payment structure'
      },
      {
        id: '2',
        type: 'optimization',
        title: 'AI Optimization Suggestion',
        description: 'Consider adding late payment penalties for better protection',
        confidence: 88,
        impact: 'medium',
        section: 'COMPENSATION',
        suggestion: 'Add 1.5% monthly interest on overdue payments'
      },
      {
        id: '3',
        type: 'risk',
        title: 'Risk Detected',
        description: 'Consider defining "pre-existing IP" more specifically',
        confidence: 92,
        impact: 'high',
        section: 'INTELLECTUAL PROPERTY',
        suggestion: 'Define scope of pre-existing intellectual property'
      }
    ]);
  }, []);

  const runAnalysis = async () => {
    setIsAnalyzing(true);
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsAnalyzing(false);
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'enhancement': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'optimization': return <Lightbulb className="h-4 w-4 text-yellow-600" />;
      case 'risk': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'compliance': return <Shield className="h-4 w-4 text-blue-600" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'enhancement': return 'border-l-green-500 bg-green-50';
      case 'optimization': return 'border-l-yellow-500 bg-yellow-50';
      case 'risk': return 'border-l-red-500 bg-red-50';
      case 'compliance': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Real-time analysis and optimization
            </h3>
          </div>
          <Button
            onClick={runAnalysis}
            disabled={isAnalyzing}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isAnalyzing ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                Run Analysis
              </>
            )}
          </Button>
        </div>

        {/* Status badges */}
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-100">
            Auto-save enabled
          </Badge>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-100">
            AI monitoring
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analysis">Live Analysis</TabsTrigger>
          <TabsTrigger value="risks">Risk Scoring</TabsTrigger>
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          <TabsTrigger value="query">Custom Query</TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="space-y-4">
          <div className="space-y-3">
            {analysisResults.map((result) => (
              <div 
                key={result.id}
                className={`border-l-4 p-4 rounded-r-lg ${getTypeColor(result.type)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2 mb-2">
                    {getTypeIcon(result.type)}
                    <span className="font-medium text-sm">{result.title}</span>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getImpactColor(result.impact)}`}
                    >
                      {result.confidence}% confidence
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-700 mb-2">{result.description}</p>
                {result.suggestion && (
                  <p className="text-xs text-gray-600 italic">{result.suggestion}</p>
                )}
                {result.section && (
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      {result.section}
                    </Badge>
                  </div>
                )}
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="risks" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Overall Risk Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{riskScores.overall}%</div>
                <p className="text-xs text-gray-600">Low Risk</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Legal Compliance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{riskScores.legal}%</div>
                <p className="text-xs text-gray-600">Excellent</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Financial Risk
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{riskScores.financial}%</div>
                <p className="text-xs text-gray-600">Moderate</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Operational
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{riskScores.operational}%</div>
                <p className="text-xs text-gray-600">Good</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          <div className="space-y-3">
            <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Lightbulb className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-sm">Smart Clause Suggestion</span>
              </div>
              <p className="text-sm text-gray-700">Add force majeure clause to protect against unforeseen circumstances</p>
            </div>
            
            <div className="p-4 border rounded-lg bg-green-50 border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="font-medium text-sm">Optimization Opportunity</span>
              </div>
              <p className="text-sm text-gray-700">Consider adding automatic renewal clause with 30-day notice period</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="query" className="space-y-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium mb-2 block">Ask AI about your contract</label>
              <Textarea
                placeholder="e.g., What are the potential risks in this contract? How can I improve the payment terms?"
                value={customQuery}
                onChange={(e) => setCustomQuery(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
            <Button className="w-full" disabled={!customQuery.trim()}>
              <Search className="h-4 w-4 mr-2" />
              Analyze Query
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIAnalysisInterface;
