import React, { useState, useEffect } from 'react';
import UnifiedModal from '@/components/ui/unified-modal';
import { DocumentPreviewEngine } from '@/engines/document-engine';
import { FileText, Download, Edit, Save, Eye } from 'lucide-react';
import type { ModalAction } from '@/components/ui/unified-modal';

interface UnifiedDocumentViewerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentId: string;
  title?: string;
  readOnly?: boolean;
  showActions?: boolean;
  onSave?: (content: string) => void | Promise<void>;
  onError?: (error: string) => void;
  onDownload?: (documentId: string) => void;
}

const UnifiedDocumentViewerModal: React.FC<UnifiedDocumentViewerModalProps> = ({
  open,
  onOpenChange,
  documentId,
  title,
  readOnly = true,
  showActions = true,
  onSave,
  onError,
  onDownload,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [documentContent, setDocumentContent] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // Reset editing state when modal opens/closes
  useEffect(() => {
    if (!open) {
      setIsEditing(false);
      setHasChanges(false);
      setSaving(false);
    }
  }, [open]);

  // Load document content when modal opens
  useEffect(() => {
    if (open && documentId) {
      setLoading(true);
      // Sample document content - in a real app, you'd fetch this from an API
      const sampleContent = `
        <h1>Document: ${title || documentId}</h1>
        <p>This is a sample document for viewing and editing purposes.</p>
        <h2>Document Content</h2>
        <p>The document content would be loaded from the backend API based on the document ID: ${documentId}</p>
        <p>This preview shows how the document would appear in the DocumentPreviewEngine.</p>
      `;
      setDocumentContent(sampleContent);
      setLoading(false);
    }
  }, [open, documentId, title]);

  const handleToggleEdit = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = async (content: string) => {
    if (!onSave) return;

    setSaving(true);
    try {
      // Handle both sync and async onSave functions
      await Promise.resolve(onSave(content));
      setHasChanges(false);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving document:', error);
      if (onError) {
        onError(error instanceof Error ? error.message : 'Failed to save document');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload(documentId);
    }
  };

  const handleContentChange = () => {
    setHasChanges(true);
  };

  // Note: DocumentPreviewEngine is read-only, so editing functionality is limited
  // For full editing capabilities, consider using DocumentEngine instead

  // Build actions array
  const actions: ModalAction[] = [];

  if (showActions) {
    if (onDownload) {
      actions.push({
        label: 'Download',
        variant: 'outline' as const,
        icon: <Download className="h-4 w-4" />,
        onClick: handleDownload,
      });
    }

    if (!readOnly) {
      if (isEditing) {
        actions.push({
          label: 'Preview',
          variant: 'outline' as const,
          icon: <Eye className="h-4 w-4" />,
          onClick: handleToggleEdit,
        });

        if (hasChanges) {
          actions.push({
            label: 'Save',
            variant: 'default' as const,
            icon: <Save className="h-4 w-4" />,
            onClick: async () => {
              // Get content from DocumentViewer and save it
              // For now, we'll pass an empty string as content
              // In a real implementation, you'd get the actual content from the editor
              await handleSave('');
            },
            loading: saving,
            disabled: saving,
          });
        }
      } else {
        actions.push({
          label: 'Edit',
          variant: 'outline' as const,
          icon: <Edit className="h-4 w-4" />,
          onClick: handleToggleEdit,
        });
      }
    }
  }

  const renderContent = () => {
    if (!documentId) {
      return (
        <div className="flex flex-col items-center justify-center py-8" role="status" aria-label="No document selected">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" aria-hidden="true" />
          <p className="text-muted-foreground">No document selected</p>
        </div>
      );
    }

    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-8" role="status" aria-label="Loading document">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4" aria-hidden="true" />
          <p className="text-muted-foreground">Loading document...</p>
        </div>
      );
    }

    return (
      <div className="h-full" role="main" aria-label={`Document viewer for ${title || 'document'}`}>
        <DocumentPreviewEngine
          content={documentContent}
          documentTitle={title || `Document ${documentId}`}
          showZoomControls={true}
          showPrintButton={true}
          showDownloadButton={true}
          showFullscreenButton={false}
          className="h-full"
        />
      </div>
    );
  };

  // Handle close with unsaved changes
  const handleClose = () => {
    if (hasChanges) {
      const confirmClose = window.confirm(
        'You have unsaved changes. Are you sure you want to close without saving?'
      );
      if (!confirmClose) return;
    }
    // Reset state when closing
    setIsEditing(false);
    setHasChanges(false);
    setSaving(false);
    onOpenChange(false);
  };

  return (
    <UnifiedModal
      open={open}
      onOpenChange={onOpenChange}
      type="dialog"
      size="5xl"
      title={title || 'Document Viewer'}
      description={isEditing ? 'Editing mode' : 'Preview mode'}
      actions={actions}
      className="h-[90vh]"
      contentClassName="h-[90vh] p-0"
      headerClassName="px-6 py-4 border-b"
      footerClassName="px-6 py-4 border-t"
      closeOnOverlayClick={!hasChanges}
      onClose={handleClose}
    >
      {renderContent()}
    </UnifiedModal>
  );
};

export default UnifiedDocumentViewerModal;
