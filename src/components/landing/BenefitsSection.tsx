import { TrendingUp, Clock, Shield, Users, Sparkles } from 'lucide-react';
import { useState } from 'react';

const BenefitsSection = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const benefits = [
    {
      stat: "75%",
      description: "faster contract creation",
      icon: Clock
    },
    {
      stat: "90%",
      description: "reduction in errors",
      icon: TrendingUp
    },
    {
      stat: "50%",
      description: "improved collaboration",
      icon: Users
    },
    {
      stat: "100%",
      description: "compliance assurance",
      icon: Shield
    }
  ];

  return (
    <section id="benefits" className="py-16 sm:py-24 lg:py-32 relative overflow-hidden">
      {/* Removed competing background to use unified background */}

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 lg:mb-20">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary mb-4 sm:mb-6">
              <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
              Proven Results
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 sm:mb-8 lg:mb-10 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-[1.15] sm:leading-[1.15] md:leading-[1.15] lg:leading-[1.15]">
              Trusted by legal professionals worldwide
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
              Join thousands of teams who have transformed their contract workflows with measurable results and proven ROI.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              const isHovered = hoveredIndex === index;
              return (
                <div
                  key={index}
                  className={`group p-6 sm:p-8 rounded-xl sm:rounded-2xl border border-border bg-card transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                    isHovered ? 'shadow-xl scale-105' : 'shadow-sm'
                  } text-center`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  <div className={`w-12 sm:w-16 h-12 sm:h-16 rounded-xl sm:rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 transition-all duration-300 ${
                    isHovered ? 'scale-110 shadow-lg' : ''
                  } bg-muted`}>
                    <IconComponent className="w-6 sm:w-8 h-6 sm:h-8 text-foreground" />
                  </div>
                  <div className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 text-foreground leading-[1.2]">{benefit.stat}</div>
                  <p className="text-sm sm:text-base lg:text-lg text-muted-foreground font-medium">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
