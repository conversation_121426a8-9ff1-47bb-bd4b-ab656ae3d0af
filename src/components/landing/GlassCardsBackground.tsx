import React from 'react';

/**
 * AnimatedGradientBackground component creates a unified, seamless background
 * that flows consistently across the entire landing page
 */
const AnimatedGradientBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {/* Unified gradient background that covers entire page */}
      <div 
        className="absolute inset-0 opacity-60"
        style={{
          background: `
            linear-gradient(135deg, 
              rgba(99, 102, 241, 0.08) 0%, 
              rgba(139, 92, 246, 0.06) 25%, 
              rgba(59, 130, 246, 0.08) 50%, 
              rgba(244, 114, 182, 0.06) 75%, 
              rgba(34, 211, 238, 0.08) 100%
            ),
            radial-gradient(ellipse at top left, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
            radial-gradient(ellipse at top right, rgba(244, 114, 182, 0.12) 0%, transparent 50%),
            radial-gradient(ellipse at bottom left, rgba(34, 211, 238, 0.10) 0%, transparent 50%),
            radial-gradient(ellipse at bottom right, rgba(139, 92, 246, 0.13) 0%, transparent 50%)
          `,
        }}
      />
      
      {/* Animated overlay for subtle movement */}
      <div 
        className="absolute inset-0 opacity-30"
        style={{
          background: 'linear-gradient(-45deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.03), rgba(59, 130, 246, 0.05), rgba(244, 114, 182, 0.03))',
          backgroundSize: '400% 400%',
          animation: 'gradient-shift 20s ease-in-out infinite',
        }}
      />

      {/* Floating elements distributed across the page */}
      <div 
        className="absolute top-20 left-10 w-96 h-96 rounded-full opacity-15"
        style={{
          background: 'radial-gradient(circle, rgba(99, 102, 241, 0.15) 0%, transparent 70%)',
          filter: 'blur(60px)',
          animation: 'float-slow 8s ease-in-out infinite',
        }}
      />
      
      <div 
        className="absolute top-1/3 right-20 w-64 h-64 rounded-full opacity-20"
        style={{
          background: 'radial-gradient(circle, rgba(244, 114, 182, 0.12) 0%, transparent 70%)',
          filter: 'blur(50px)',
          animation: 'float-medium 6s ease-in-out infinite',
        }}
      />
      
      <div 
        className="absolute top-2/3 left-1/3 w-80 h-80 rounded-full opacity-12"
        style={{
          background: 'radial-gradient(circle, rgba(34, 211, 238, 0.10) 0%, transparent 70%)',
          filter: 'blur(70px)',
          animation: 'float-fast 4s ease-in-out infinite',
        }}
      />

      <div 
        className="absolute bottom-20 right-1/4 w-72 h-72 rounded-full opacity-18"
        style={{
          background: 'radial-gradient(circle, rgba(139, 92, 246, 0.14) 0%, transparent 70%)',
          filter: 'blur(55px)',
          animation: 'float-slow 7s ease-in-out infinite reverse',
        }}
      />
    </div>
  );
};

export default AnimatedGradientBackground;
