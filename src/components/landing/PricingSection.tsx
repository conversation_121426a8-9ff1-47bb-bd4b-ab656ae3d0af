import { Button } from '@/components/ui/button';
import { CheckCircle, Sparkles, Crown, Building } from 'lucide-react';
import { useState } from 'react';

interface PricingSectionProps {
  onGetStarted: () => void;
}

const PricingSection = ({ onGetStarted }: PricingSectionProps) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const plans = [
    {
      name: "Starter",
      price: "$19",
      period: "per month",
      description: "Perfect for solo practitioners and small firms",
      features: [
        "1 workspace",
        "1 user",
        "3 contracts per month",
        "Basic AI analysis",
        "Standard templates (10+)",
        "Email support",
        "Document storage (1GB)",
        "Basic reporting"
      ],
      icon: <PERSON>rk<PERSON>,
      highlight: "Great for getting started"
    },
    {
      name: "Professional",
      price: "$49",
      period: "per user/month",
      description: "For growing legal teams and departments",
      features: [
        "3 workspaces",
        "Up to 10 users",
        "50 contracts per month",
        "Advanced AI analysis & risk scoring",
        "Premium templates (100+)",
        "Priority email & chat support",
        "Advanced workflows & approvals",
        "Document storage (50GB)",
        "Analytics & insights",
        "E-signature integration"
      ],
      isPopular: true,
      icon: Crown,
      highlight: "Most popular choice"
    },
    {
      name: "Enterprise",
      price: "$99",
      period: "per user/month",
      description: "For large organizations with complex needs",
      features: [
        "Unlimited workspaces",
        "Unlimited users",
        "Unlimited contracts",
        "Custom AI models & training",
        "Custom templates & clauses",
        "24/7 dedicated support",
        "Advanced security & compliance",
        "Unlimited document storage",
        "Custom integrations & API",
        "On-premise deployment option",
        "Advanced analytics & reporting",
        "White-label options"
      ],
      icon: Building,
      highlight: "Maximum flexibility & control"
    }
  ];

  return (
    <section id="pricing" className="py-16 sm:py-24 lg:py-32 relative overflow-hidden">
      {/* Removed competing background to use unified background */}

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 lg:mb-20">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary mb-4 sm:mb-6">
              <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
              Pricing Plans
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 sm:mb-8 lg:mb-10 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-[1.15] sm:leading-[1.15] md:leading-[1.15] lg:leading-[1.15]">
              Simple, transparent pricing
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
              Choose the plan that fits your needs. All plans include our core features with no hidden fees and 14-day free trial.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => {
              const IconComponent = plan.icon;
              const isHovered = hoveredIndex === index;
              return (
                <div
                  key={index}
                  className={`group relative rounded-2xl sm:rounded-3xl p-6 sm:p-8 border-2 transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
                    plan.isPopular
                      ? 'border-primary shadow-xl lg:scale-105 bg-card'
                      : 'border-border shadow-lg bg-card'
                  } ${isHovered ? 'shadow-2xl lg:scale-105' : ''}`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {plan.isPopular && (
                    <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary text-primary-foreground text-xs sm:text-sm px-4 sm:px-6 py-1.5 sm:py-2 rounded-full font-bold shadow-lg">
                        Most Popular
                      </span>
                    </div>
                  )}

                  {/* Icon */}
                  <div className="flex justify-center mb-4 sm:mb-6">
                    <div className={`w-12 sm:w-16 h-12 sm:h-16 rounded-xl sm:rounded-2xl flex items-center justify-center transition-all duration-300 ${
                      isHovered ? 'scale-110 shadow-lg' : ''
                    } bg-muted border border-border`}>
                      <IconComponent className={`w-6 sm:w-8 h-6 sm:h-8 ${plan.isPopular ? 'text-primary' : 'text-foreground'}`} />
                    </div>
                  </div>

                  <div className="text-center mb-6 sm:mb-8">
                    <h3 className="text-xl sm:text-2xl font-bold mb-2 leading-[1.3]">{plan.name}</h3>
                    <p className="text-sm sm:text-base text-muted-foreground mb-2 px-2 leading-relaxed">{plan.description}</p>
                    {plan.highlight && (
                      <p className="text-xs sm:text-sm text-primary font-medium mb-3 sm:mb-4 leading-relaxed">{plan.highlight}</p>
                    )}
                    <div className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2 leading-[1.2]">{plan.price}</div>
                    <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">{plan.period}</p>
                  </div>

                  <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <div className="w-5 sm:w-6 h-5 sm:h-6 bg-muted rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="w-3 sm:w-4 h-3 sm:h-4 text-foreground" />
                        </div>
                        <span className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-relaxed">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant={plan.isPopular ? "default" : "outline"}
                    size="lg"
                    className="w-full text-sm sm:text-base lg:text-lg py-4 sm:py-6 h-auto transition-all duration-300 hover:scale-105 font-medium"
                    onClick={plan.name === "Enterprise" ? undefined : onGetStarted}
                  >
                    {plan.name === "Enterprise" ? "Contact Sales" : "Start Free Trial"}
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
