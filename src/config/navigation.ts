import {
  Bar<PERSON>hart3,
  FileText,
  <PERSON>olderOpen,
  ThumbsUp,
  Users,
  BookText,
  LayoutDashboard,
  Settings,
  FileSignature,
} from "lucide-react";

export interface NavigationItem {
  /**
   * The path to navigate to
   */
  path: string;

  /**
   * The label to display in the navigation
   */
  label: string;

  /**
   * The icon to display next to the label
   */
  icon: React.ComponentType<{ className?: string }>;

  /**
   * Whether this item is active when the path starts with this item's path
   */
  matchSubpaths?: boolean;

  /**
   * The permission required to view this item
   */
  requiredPermission?: string;

  /**
   * Any of these permissions will allow access
   */
  anyPermissions?: string[];

  /**
   * All of these permissions are required for access
   */
  allPermissions?: string[];

  /**
   * Badge count to display (optional)
   */
  badgeCount?: number | (() => number);

  /**
   * Whether this item should be hidden from the navigation
   */
  hidden?: boolean;
}

export interface NavigationSection {
  /**
   * The title of the section
   */
  title: string;

  /**
   * The items in this section
   */
  items: NavigationItem[];
}

/**
 * The main navigation configuration
 */
export const navigationConfig: NavigationSection[] = [
  {
    title: "MAIN NAVIGATION",
    items: [
      {
        path: "/app/dashboard",
        label: "Dashboard",
        icon: LayoutDashboard,
        // Everyone can see the dashboard
      },
      {
        path: "/app/contracts",
        label: "Contracts",
        icon: FileText,
        matchSubpaths: true,
        // Require contracts.view permission
        requiredPermission: "contracts.view",
      },
      {
        path: "/app/repository",
        label: "Repository",
        icon: FolderOpen,
        matchSubpaths: true,
        // Require templates.view permission
        requiredPermission: "templates.view",
      },
      {
        path: "/app/approvals",
        label: "Approvals",
        icon: ThumbsUp,
        matchSubpaths: true,
        // Need either contracts.approve or templates.approve
        anyPermissions: ["contracts.approve", "templates.approve"],
        // Dynamic badge count (would be implemented with a hook in real app)
        badgeCount: 3,
      },
    ],
  },
  {
    title: "TOOLS & RESOURCES",
    items: [
      {
        path: "/app/analytics",
        label: "Analytics",
        icon: BarChart3,
        matchSubpaths: true,
        // Require analytics.view permission
        requiredPermission: "analytics.view",
      },
      {
        path: "/app/workspaces",
        label: "Workspaces",
        icon: Users,
        matchSubpaths: true,
        // Require workspace.manage permission
        requiredPermission: "workspace.manage",
      },
      {
        path: "/app/clause-library",
        label: "Clause Library",
        icon: BookText,
        matchSubpaths: true,
        // Need either contracts.view or templates.view
        anyPermissions: ["contracts.view", "templates.view"],
      },
      {
        path: "/app/signatures",
        label: "Signatures",
        icon: FileSignature,
        matchSubpaths: true,
        // Need contracts.view permission
        requiredPermission: "contracts.view",
      },
    ],
  },

  {
    title: "",
    items: [
      {
        path: "/app/settings",
        label: "Settings",
        icon: Settings,
        // Everyone can see settings
      },
    ],
  },
];

/**
 * Get all navigation items flattened into a single array
 */
export function getAllNavigationItems(): NavigationItem[] {
  return navigationConfig.flatMap(section => section.items);
}

/**
 * Get a navigation item by path
 */
export function getNavigationItemByPath(path: string): NavigationItem | undefined {
  return getAllNavigationItems().find(item => {
    if (item.matchSubpaths) {
      return path.startsWith(item.path);
    }
    return path === item.path;
  });
}

/**
 * Check if a path is active
 */
export function isPathActive(currentPath: string, itemPath: string, matchSubpaths = false): boolean {
  if (matchSubpaths) {
    return currentPath.startsWith(itemPath);
  }
  return currentPath === itemPath;
}
