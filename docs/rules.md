# Development Guidelines

This document outlines the development standards, practices, and workflows for the Contract Management System project. Following these guidelines ensures consistency, quality, and maintainability across the codebase.

## ⚠️ IMPORTANT

- Always read `legalai.md` before writing any code
- After adding a major feature or completing a milestone, update `legalai.md`
- Document the entire database schema in `legalai.md`
- For new migrations, make sure to add them to the same file
- Never proceed with implementation if legal compliance is unclear

## 🤔 General Practices

- Begin every response with this emoji
- Use simple and easy-to-understand language
- Consider the full context of code before making changes
- Always check and fix TypeScript errors
- Always check terminal output for errors and warnings
- Use consistent port configurations; if a port is already in use, terminate the process and restart
- Create reusable components for all UI elements before implementation
- Follow all instructions and best practices in this document
- Don't always agree with me. Think and give me honest suggestions
- Retry failed tool calls
- Important: Try to fix things at the cause, not the symptom
- Document code as you write it, not after
- Seek clarification when requirements are ambiguous
- Avoid assumptions about implementation details
- always read all the lines of a file 

## 📋 Fundamental Principles

- Write clean, simple, readable code
- Prioritize reliability above all else—if it can't be reliable, don't build it
- Implement features in the simplest possible way
- Keep files small and focused on a single responsibility
- Test after every meaningful change
- Focus on core functionalities before optimization
- Use clear, consistent naming conventions
- Think thoroughly before coding; write 2-3 reasoning paragraphs when planning
- Leave ego aside when debugging and fixing errors—nobody knows everything
- Don't remove existing features when implementing new ones unless explicitly instructed
- Follow the DRY (Don't Repeat Yourself) principle, but not at the expense of readability
- Embrace YAGNI (You Aren't Gonna Need It) - don't add functionality until it's necessary
- Consider maintainability as a first-class concern

## 🧩 Function Design

- A function should perform a single task (Single Responsibility Principle)
- Split large functions into smaller, focused helper functions
- Write meaningful comments that explain *why* a piece of code exists, not just what it does
- Use docstrings for functions and classes following language-specific standards
- Avoid redundant comments that simply repeat what the code already expresses
- Keep functions short (generally under 30 lines)
- Use meaningful parameter and return type annotations
- Function arguments should be minimal and straightforward
- Favor pure functions when possible
- Use appropriate design patterns, but don't over-engineer
- Consider error cases and edge conditions from the start

## ⚠️ Error Handling

- Use try-catch blocks wisely—catch specific exceptions rather than using broad catch statements
- Always log errors with enough context to help with future debugging
- Use custom error messages that clearly describe what went wrong
- Consider multiple possible causes before deciding on a solution
- Explain problems in plain English
- Make minimal necessary changes, modifying as few lines of code as possible
- Always verify that the fix resolves the issue
- Create custom error classes for domain-specific errors
- Implement proper fallback mechanisms for critical operations
- Never silently catch errors without proper handling
- Use error boundaries in React components to prevent cascading failures
- Include stack traces for debugging in non-production environments

## 📖 Code Readability

- Use meaningful variable names; avoid one-letter names except in specific contexts (e.g., loop counters)
- Limit line length according to language guidelines (80-100 characters)
- Group related code logically; separate concerns using functions, classes, or modules
- Use whitespace effectively to separate logical blocks of code
- Follow consistent indentation patterns
- Use destructuring and modern syntax features for cleaner code
- Avoid nested conditionals beyond 2-3 levels
- Keep nesting of loops to a minimum
- Use early returns to reduce complexity
- Use consistent code formatting (preferably enforced by tools like Prettier)
- Group related properties and methods in classes
- Favor composition over inheritance

## ⚡ Performance Optimization

- Avoid premature optimization; focus on clean, maintainable code first
- Use efficient data structures for the task (e.g., sets for unique values, dictionaries for key-value lookups)
- Minimize API calls and I/O operations; batch operations when possible
- Use caching appropriately to store results of repeated computations
- Implement pagination for large data sets
- Use memoization for expensive function calls
- Consider the impact of render cycles in React components
- Profile before optimizing - find actual bottlenecks
- Optimize critical paths and hot code paths first
- Use windowing techniques for long lists in UI
- Consider code splitting and lazy loading for large applications
- Implement proper database indexing strategies
- Use server-side filtering and sorting for large datasets

## 🔒 Security

- Sanitize all user input to prevent SQL injection, XSS, and other attacks
- Never hardcode sensitive information; use environment variables for API keys, passwords, and secrets
- Use HTTPS and secure protocols to ensure data encryption during transmission
- Use actively maintained libraries to avoid known vulnerabilities
- Implement proper authentication and authorization checks
- Validate all data on both client and server sides
- Follow the principle of least privilege for database access
- Implement CSP (Content Security Policy) headers
- Use parameterized queries for database operations
- Implement proper CORS policies
- Use secure HTTP-only cookies for sensitive information
- Implement rate limiting for authentication endpoints
- Regularly update dependencies to patch security vulnerabilities
- Follow OWASP security best practices

## 🔄 Refactoring and Code Review

- Refactor regularly to improve code readability, reduce complexity, and eliminate duplication
- Use linting tools like ESLint (JavaScript/TypeScript) or Pylint (Python) to enforce consistent style
- Conduct thorough code reviews focusing on logic, security, and performance
- Look for edge cases during code reviews
- Provide constructive feedback in code reviews
- Use automated tests to validate refactored code
- Refactor one step at a time with tests between steps
- Be cautious when refactoring core business logic
- Review your own code before submitting for review
- Create small, focused pull requests that are easier to review
- Use code review checklists for consistency
- Consider technical debt implications in every review

## 🧪 Testing Guidelines

### Frontend Testing

- Write unit tests for utilities and helper functions
- Create component tests for reusable UI components
- Implement integration tests for complex workflows
- Test for both success and failure cases
- Mock API calls and external dependencies
- Use testing-library/react for React component testing
- Test accessibility compliance
- Implement visual regression testing for UI components
- Write end-to-end tests for critical user journeys
- Test different screen sizes and responsive behaviors
- Test different browser compatibility as needed
- Implement snapshot testing judiciously

### Backend Testing

- Write unit tests for individual functions and methods
- Create integration tests for API endpoints
- Implement database tests with proper setup and teardown
- Test validation logic thoroughly
- Use pytest for Python testing
- Use test fixtures to set up test data
- Mock external services in tests
- Test authentication and authorization flows
- Implement performance tests for critical endpoints
- Test database migrations
- Ensure tests can run in isolation and in parallel
- Test rate limiting and error handling
- Include API contract tests

## 📁 Project Structure and Organization

### Frontend Structure

- Organize components by feature or domain
- Keep related files together
- Use consistent file naming conventions
- Split large components into smaller, focused components
- Organize styles alongside components
- Keep business logic separate from UI components
- Use a consistent pattern for state management
- Group related constants and utilities
- Implement a clear separation between UI and data layers
- Use type definitions for shared interfaces
- Structure routes logically to match user flow
- Consider micro-frontends for large applications

### Backend Structure

- Organize code by domain or feature
- Follow a layered architecture (controllers, services, repositories)
- Keep business logic in service layer
- Implement repositories for data access
- Use dependency injection for better testability
- Keep API endpoints thin, delegating to services
- Separate configuration from application code
- Implement clear domain boundaries
- Use middleware for cross-cutting concerns
- Structure logging consistently across the application
- Implement clear error boundaries
- Use interfaces to define contracts between layers

## 🔄 Version Control Practices

- Write clear, descriptive commit messages
- Use conventional commit format: `type(scope): message`
- Create feature branches for new functionality
- Keep pull requests focused on a single concern
- Rebase feature branches before merging
- Squash commits when appropriate
- Never commit directly to main/master branch
- Use git hooks to enforce code quality
- Regularly sync with the main branch to reduce merge conflicts
- Tag releases with semantic versioning
- Document breaking changes clearly
- Keep sensitive information out of version control
- Use .gitignore properly to exclude build artifacts and dependencies

## 📚 Documentation Standards

- Document all public APIs with clear descriptions and examples
- Maintain up-to-date README files
- Create and update technical documentation alongside code changes
- Document configuration options and environment variables
- Create diagrams for complex workflows or architectures
- Document database schema changes
- Write release notes for significant updates
- Create onboarding documentation for new team members
- Document known limitations and edge cases
- Keep a decision log for major architectural choices
- Include troubleshooting guides for common issues
- Document external dependencies and integration points
- Use standardized documentation formats (e.g., OpenAPI for APIs)

## 🚀 Deployment Guidelines

- Follow the deployment procedure in the [Deployment Guide](deployment.md)
- Test changes in a staging environment before production
- Use environment-specific configuration
- Implement continuous integration and deployment
- Monitor deployments for unexpected behavior
- Have a rollback plan for failed deployments
- Use feature flags for risky changes
- Implement zero-downtime deployment strategies
- Automate the deployment pipeline
- Implement proper logging and monitoring for production
- Set up alerts for critical issues
- Create thorough deployment checklists
- Document each deployment with detailed release notes
- Implement database migration strategies for zero-downtime

## 🌐 Frontend-Specific Guidelines

### React Best Practices

- Use functional components and hooks
- Implement proper state management based on complexity
- Optimize rendering with React.memo, useMemo, and useCallback
- Use context API for global state when appropriate
- Implement proper form handling with react-hook-form
- Use TypeScript for type safety
- Follow accessibility guidelines (WCAG 2.1)
- Implement proper error boundaries
- Use React Suspense for code splitting
- Create custom hooks for reusable logic
- Implement proper loading states
- Use proper key strategies in lists
- Minimize prop drilling with contextual state

### CSS and Styling

- Use Tailwind CSS for styling
- Maintain consistent spacing and sizing
- Use design tokens for colors, typography, and spacing
- Ensure responsive design for all screen sizes
- Implement dark mode compatibility
- Keep CSS modules or styled components scoped to components
- Use a mobile-first approach to responsive design
- Implement fluid typography for better responsiveness
- Use CSS variables for theming
- Optimize CSS for performance (minimize specificity, avoid complex selectors)
- Consider accessibility in styling (contrast, focus states)
- Implement print styles where appropriate
- Test styling across different browsers

## 🔧 Backend-Specific Guidelines

### FastAPI Best Practices

- Use Pydantic for request/response models
- Implement proper dependency injection
- Organize endpoints logically by domain
- Use appropriate HTTP methods and status codes
- Implement proper request validation
- Document APIs with docstrings for Swagger UI
- Use background tasks for long-running operations
- Implement proper error handling middleware
- Use async/await for I/O-bound operations
- Implement proper request logging
- Use path operations for clear API design
- Implement proper rate limiting
- Use FastAPI middleware for cross-cutting concerns

### Database Practices

- Use SQLAlchemy ORM for database operations
- Implement database migrations for schema changes
- Write efficient queries to minimize database load
- Use indexes for frequently queried fields
- Implement proper transaction management
- Follow naming conventions for database objects
- Use Alembic for database migrations
- Implement proper connection pooling
- Use database constraints to enforce data integrity
- Implement soft deletes for important data
- Use appropriate data types
- Implement database-level validation
- Consider sharding strategies for large datasets
- Implement proper query optimization

## 🧠 Problem-Solving Approach

1. **Understand the problem**
   - Clearly define what needs to be solved
   - Identify requirements and constraints
   - Break down complex problems into smaller parts
   - Create acceptance criteria for the solution
   - Document assumptions

2. **Research and explore**
   - Look for existing solutions or patterns
   - Consult documentation and resources
   - Explore potential approaches
   - Consider multiple solutions
   - Evaluate trade-offs between approaches

3. **Plan before coding**
   - Outline the solution before implementation
   - Consider edge cases and potential pitfalls
   - Think about testing strategy
   - Create a task breakdown
   - Estimate complexity and effort

4. **Implement incrementally**
   - Start with a minimal working solution
   - Add features and complexity gradually
   - Test each increment
   - Refactor as necessary
   - Document as you go

5. **Review and refine**
   - Review your own code first
   - Seek feedback from others
   - Refine based on feedback and testing
   - Consider performance implications
   - Verify against original requirements

## 🚦 Development Workflow

1. **Feature planning**
   - Understand requirements
   - Create subtasks if needed
   - Estimate effort
   - Define clear acceptance criteria
   - Create technical design documents if necessary

2. **Development**
   - Create feature branch
   - Implement code following guidelines
   - Write tests
   - Document changes
   - Address technical debt proactively

3. **Code review**
   - Submit pull request
   - Address review comments
   - Get approval
   - Ensure CI tests pass
   - Update documentation as needed

4. **Testing**
   - Run automated tests
   - Perform manual testing
   - Verify acceptance criteria
   - Test edge cases
   - Perform performance testing if needed

5. **Deployment**
   - Merge to main branch
   - Deploy to staging
   - Verify in staging
   - Deploy to production
   - Monitor for issues
   - Document release details

## 💻 Developer Experience

1. **Environment Setup**
   - Document clear setup instructions
   - Use containerization for consistent environments
   - Automate environment setup when possible
   - Keep dependencies updated
   - Use consistent tooling across the team

2. **Local Development**
   - Use hot reloading for faster development
   - Implement debugging tools
   - Create helpful developer scripts
   - Use feature flags for work-in-progress features
   - Set up local testing environments

3. **Continuous Learning**
   - Share knowledge through documentation
   - Conduct regular tech talks or learning sessions
   - Encourage experimentation with new technologies
   - Create spaces for technical discussions
   - Stay updated with industry best practices